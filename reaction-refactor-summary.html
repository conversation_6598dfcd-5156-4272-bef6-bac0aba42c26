<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>FD Member 交互模块（Reaction）后端重构总结</title>
<style>
 body{font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Robot<PERSON>,"Helvetica Neue",<PERSON><PERSON>,"Noto Sans",sans-serif;line-height:1.6;color:#333;padding:2rem;max-width:1000px;margin:auto}
 h1,h2{color:#1a73e8}
 pre,code{background:#f5f5f5;padding:2px 4px;border-radius:4px}
 section{margin-bottom:2.5rem}
</style>
</head>
<body>
<h1>FD Member ▶ 交互模块（Reaction）后端重构总结</h1>
<p>本文档记录 <strong>点赞、收藏、推荐</strong> 三大交互功能的后端重构全过程、重构后功能架构与关键实现，以及新的 GraphQL API 用法，供开发与维护团队查阅。</p>

<section>
<h2>1. 重构目标</h2>
<ul>
<li>消除 <code>likes / bookmarks / recommends</code> 三套重复代码与数据表</li>
<li>统一数据层、服务层、GraphQL API、后台管理与通知</li>
<li>为未来扩展更多交互类型（如 <code>dislike</code>）提供范式</li>
</ul>
</section>

<section>
<h2>2. 实施过程</h2>
<ol>
<li><strong>阶段 0-1</strong>：创建新表 <code>fd_reactions</code>（插件激活 &amp; init 自动创建）</li>
<li><strong>阶段 2</strong>：实现 <code>ReactionService</code>（统一增删查计数 + 触发事件）</li>
<li><strong>阶段 3</strong>：统一通知<br>监听 <code>fd_reaction_added / updated</code> → 发送作者通知</li>
<li><strong>阶段 4</strong>：统一 GraphQL<br>
  <ul>
   <li>枚举 <code>ReactionTypeEnum</code>（LIKE / BOOKMARK / RECOMMEND）</li>
   <li>Mutation <code>addReaction</code> / <code>removeReaction</code></li>
   <li>在 <code>ContentNode</code> 注入 <code>reactionCount</code> / <code>userHasReacted</code></li>
  </ul>
</li>
<li><strong>阶段 5</strong>：统一后台管理页 <code>reactions-manager.php</code></li>
<li><strong>阶段 6</strong>：统一统计缓存 <code>reaction-stats.php</code></li>
<li><strong>阶段 7</strong>：删除旧目录与文件，清理数据库旧表</li>
</ol>
</section>

<section>
<h2>3. 重构后目录与核心文件</h2>
<ul>
<li><code>includes/reactions/
  ├─ reaction-core.php        # 创建数据表
  ├─ class-reaction-service.php
  ├─ reaction-notifications.php
  ├─ reaction-stats.php
  └─ reactions-graphql.php</code></li>
<li><code>admin/reactions-manager.php</code> – 通用后台列表</li>
</ul>
</section>

<section>
<h2>4. 数据表结构（fd_reactions）</h2>
<pre><code>id          BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY
type        ENUM('like','bookmark','recommend') NOT NULL
user_id     BIGINT UNSIGNED NOT NULL
post_id     BIGINT UNSIGNED NOT NULL
status      TINYINT(1) DEFAULT 1                -- 1=有效 0=取消
created_at  DATETIME DEFAULT CURRENT_TIMESTAMP
updated_at  DATETIME ON UPDATE CURRENT_TIMESTAMP
UNIQUE KEY  (type,user_id,post_id)
KEY post_type_status (post_id,type,status)
KEY user_type        (user_id,type)</code></pre>
</section>

<section>
<h2>5. GraphQL API</h2>
<h3>5.1 枚举</h3>
<pre><code>enum ReactionTypeEnum { LIKE BOOKMARK RECOMMEND }</code></pre>

<h3>5.2 Mutations</h3>
<pre><code># 添加交互
mutation AddReaction($id: ID!) {
  addReaction(input: {type: LIKE, postId: $id}) {
    success
    message
  }
}

# 取消交互
mutation RemoveReaction($id: ID!) {
  removeReaction(input: {type: BOOKMARK, postId: $id}) {
    success
    message
  }
}</code></pre>

<h3>5.3 查询字段</h3>
<pre><code># 单篇文章
query GetReactions($id: ID!) {
  contentNode(id: $id, idType: DATABASE_ID) {
    title
    likeCount: reactionCount(type: LIKE)
    userHasLiked: userHasReacted(type: LIKE)
    bookmarkCount: reactionCount(type: BOOKMARK)
    userHasBookmarked: userHasReacted(type: BOOKMARK)
    recommendCount: reactionCount(type: RECOMMEND)
    userHasRecommended: userHasReacted(type: RECOMMEND)
  }
}

# 文章列表
query ListWithReactions {
  posts(first: 5) {
    nodes {
      title
      reactionCount(type: LIKE)
      userHasReacted(type: LIKE)
    }
  }
}</code></pre>
<p>• <code>postId</code> 参数既可传全局 ID，也可直接传数据库 ID。<br>• 查询字段适用于所有实现 <code>ContentNode</code> 接口的类型（自定义文章类型亦可）。</p>
</section>

<section>
<h2>6. 通知与后台</h2>
<ul>
<li>统一事件 <code>fd_reaction_added / updated</code>，通知模板按 <code>$type</code> 分支生成。</li>
<li>后台菜单「交互管理」支持 Tab 切换（点赞 / 收藏 / 推荐）。</li>
<li>文章列表新增三列：点赞数 / 收藏数 / 推荐数。</li>
</ul>
</section>

<section>
<h2>7. 测试结果</h2>
<ul>
<li>添加 / 取消 三种交互 → Success ✅</li>
<li>单篇查询、列表查询 → 返回正确交互数据 ✅</li>
<li>普通 Post 与自定义 Type 均通过测试 ✅</li>
<li>Schema <code>extensions.debug</code> 为空，无重复字段或接口错误 ✅</li>
</ul>
</section>

<section>
<h2>8. 后续建议</h2>
<ul>
<li>前端全部替换成通用 API，弃用旧别名字段。</li>
<li>若需新增交互类型，只需：<br>① 在枚举中加入新值 → ② ReactionService 支持 → ③ 后台与通知模板分支。</li>
<li>为 <code>fd_reactions</code> 表添加行级缓存或分区策略，以支撑高并发。</li>
</ul>
</section>

<p style="text-align:right;color:#666">最后更新：<script>document.write(new Date().toLocaleString())</script></p>
</body>
</html> 