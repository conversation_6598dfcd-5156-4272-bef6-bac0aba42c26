<?php
/**
 * GraphQL integration for Content Access Control.
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Explicitly require the core file to ensure constants and functions are available.
require_once __DIR__ . '/cac-core.php';
require_once __DIR__ . '/cac-db.php'; // 确保数据库函数可用

/**
 * 解析付费墙HTML，提取预览内容和链接信息
 * @param string $content 包含付费墙的HTML内容
 * @return array|null 解析结果或null
 */
function fd_member_parse_paywall_html($content) {
    if (strpos($content, 'fd-member-access-denied') === false) {
        return null;
    }

    // 使用DOMDocument解析HTML
    $dom = new DOMDocument();
    libxml_use_internal_errors(true); // 抑制HTML解析警告
    $dom->loadHTML('<?xml encoding="utf-8" ?>' . $content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

    $xpath = new DOMXPath($dom);
    $paywall_div = $xpath->query('//div[contains(@class, "fd-member-access-denied")]')->item(0);

    if (!$paywall_div) {
        return null;
    }

    // 提取链接信息
    $links = $xpath->query('.//a', $paywall_div);
    $login_url = '/auth/login';
    $register_url = '/auth/register';
    $upgrade_url = '/membership/upgrade';

    foreach ($links as $link) {
        $href = $link->getAttribute('href');
        $text = strtolower($link->textContent);

        if (strpos($text, 'log in') !== false || strpos($text, '登录') !== false) {
            $login_url = $href;
        } elseif (strpos($text, 'register') !== false || strpos($text, '注册') !== false) {
            $register_url = $href;
        } elseif (strpos($text, 'upgrade') !== false || strpos($text, '升级') !== false) {
            $upgrade_url = $href;
        }
    }

    // 获取付费墙消息
    $message = $paywall_div->textContent;

    // 移除付费墙div，获取预览内容
    $paywall_div->parentNode->removeChild($paywall_div);
    $preview_content = $dom->saveHTML();

    // 清理XML声明
    $preview_content = preg_replace('/^<\?xml[^>]*\?>/', '', $preview_content);

    return [
        'preview_content' => $preview_content,
        'login_url' => $login_url,
        'register_url' => $register_url,
        'upgrade_url' => $upgrade_url,
        'message' => trim($message)
    ];
}

add_action('graphql_register_types', function () {

    // Prepare resolver callbacks
    $required_member_resolver = function ($post) {
        return intval(get_post_meta($post->ID, FD_POST_ACCESS_LEVEL_META_KEY, true));
    };

    $is_members_only_resolver = function ($post) {
        $level = intval(get_post_meta($post->ID, FD_POST_ACCESS_LEVEL_META_KEY, true));
        return $level !== 0;
    };

    $unlock_price_resolver = function ($post) {
        return floatval(get_post_meta($post->ID, FD_POST_UNLOCK_PRICE_META_KEY, true));
    };

    $is_unlocked_by_user_resolver = function ($post) {
        $user_id = get_current_user_id();
        if (!$user_id) {
            return false;
        }

        // 首先检查用户是否通过会员等级有权限访问
        if (fd_member_user_can_view_post($post->ID)) {
            return true;
        }

        // 如果会员等级不够，检查是否单独解锁了文章
        return fd_member_has_user_unlocked_post($user_id, $post->ID);
    };

    // 新增：付费墙信息解析器
    $paywall_info_resolver = function ($post) {
        // 检查是否有付费墙限制
        $required_level = intval(get_post_meta($post->ID, FD_POST_ACCESS_LEVEL_META_KEY, true));
        $unlock_price = floatval(get_post_meta($post->ID, FD_POST_UNLOCK_PRICE_META_KEY, true));

        if ($required_level === 0 && $unlock_price <= 0) {
            return null; // 无付费墙
        }

        // 获取受限内容（应用付费墙过滤器）
        $restricted_content = apply_filters('the_content', $post->post_content);

        // 解析付费墙HTML，提取预览内容和链接信息
        $paywall_data = fd_member_parse_paywall_html($restricted_content);

        if (!$paywall_data) {
            return null;
        }

        return [
            'hasPaywall' => true,
            'previewContent' => $paywall_data['preview_content'],
            'loginUrl' => $paywall_data['login_url'] ?: '/auth/login',
            'registerUrl' => $paywall_data['register_url'] ?: '/auth/register',
            'upgradeUrl' => $paywall_data['upgrade_url'] ?: '/membership/upgrade',
            'message' => $paywall_data['message'],
            'isLoggedIn' => is_user_logged_in()
        ];
    };

    $content_resolver = function ($post, array $args) {

        // Check if the user has permission to view the post.
        if (fd_member_user_can_view_post($post->ID)) {
            // User has access. Temporarily remove our filter to avoid recursion
            // when applying 'the_content' filters.
            remove_filter('the_content', 'fd_member_filter_the_content', 10);

            $content = $post->post_content;
            // WPGraphQL's default behavior is to apply 'the_content' filter for 'rendered' format.
            if (isset($args['format']) && 'rendered' === $args['format']) {
                $content = apply_filters('the_content', $content);
            }

            // Add the filter back for other contexts.
            add_filter('the_content', 'fd_member_filter_the_content', 10);

            return $content;

        } else {
            // User does not have access. Return a preview.
            $excerpt = fd_member_generate_preview($post->ID);
            $message = '<div class="fd-member-access-denied">';
    
            $login_link    = get_option('fd_member_login_url');
            $register_link = get_option('fd_member_register_url');
            $upgrade_link  = get_option('fd_member_upgrade_url');

            if (!$login_link)    { $login_link    = wp_login_url(get_permalink($post->ID)); }
            if (!$register_link) { $register_link = wp_registration_url(); }
            if (!$upgrade_link)  { $upgrade_link  = home_url('/membership/upgrade'); }

            if (is_user_logged_in()) {
                $message .= '<p>' . __('This content is reserved for a higher membership level.', 'fd-member') . '</p>';
                $message .= '<a href="' . esc_url($upgrade_link) . '" class="button">' . __('Upgrade Your Membership', 'fd-member') . '</a>';
            } else {
                $message .= '<p>' . __('Please log in or register to view this content.', 'fd-member') . '</p>';
                $message .= '<a href="' . esc_url($login_link) . '" class="button">' . __('Log In', 'fd-member') . '</a> ';
                $message .= '<a href="' . esc_url($register_link) . '" class="button">' . __('Register', 'fd-member') . '</a>';
            }
            
            $message .= '</div>';

            return $excerpt . $message;
        }
    };

    // Loop through all post types that are exposed to GraphQL
    foreach (get_post_types(['show_in_graphql' => true], 'objects') as $ptype => $obj) {
        $gql_type = $obj->graphql_single_name; // e.g., Post, Book

        // requiredMemberLevel
        register_graphql_field($gql_type, 'requiredMemberLevel', [
            'type'        => 'Int',
            'description' => __('The minimum member level ID required to view the full content.', 'fd-member'),
            'resolve'     => $required_member_resolver,
        ]);

        // isMembersOnly
        register_graphql_field($gql_type, 'isMembersOnly', [
            'type'        => 'Boolean',
            'description' => __('Whether the post requires membership to view.', 'fd-member'),
            'resolve'     => $is_members_only_resolver,
        ]);

        // unlockPrice
        register_graphql_field($gql_type, 'unlockPrice', [
            'type'        => 'Float',
            'description' => __('The price to unlock this post individually.', 'fd-member'),
            'resolve'     => $unlock_price_resolver,
        ]);

        // isUnlockedByCurrentUser
        register_graphql_field($gql_type, 'isUnlockedByCurrentUser', [
            'type'        => 'Boolean',
            'description' => __('Whether the current user has unlocked this post.', 'fd-member'),
            'resolve'     => $is_unlocked_by_user_resolver,
        ]);

        // paywallInfo - 新增付费墙信息字段
        register_graphql_field($gql_type, 'paywallInfo', [
            'type'        => 'PaywallInfo',
            'description' => __('Parsed paywall information including preview content and links.', 'fd-member'),
            'resolve'     => $paywall_info_resolver,
        ]);

        // 注意：content 字段将在循环外统一到 ContentNode 接口注册
    }

    // 统一覆盖 ContentNode 接口的 content 字段，避免重复注册
    if ( function_exists( 'unregister_graphql_field' ) ) {
        unregister_graphql_field( 'ContentNode', 'content' );
    }

    register_graphql_field( 'ContentNode', 'content', [
            'type'        => 'String',
            'args'        => [
                'format' => [
                    'type'        => 'PostObjectFieldFormatEnum',
                'description' => __( 'The format of the Post Content to be returned.', 'wp-graphql' ),
            ],
        ],
        'description' => __( 'The content of the post, protected by membership level.', 'fd-member' ),
            'resolve'     => $content_resolver,
    ] );

    // 注册PaywallInfo类型
    register_graphql_object_type('PaywallInfo', [
        'description' => __('Information about paywall content and configuration.', 'fd-member'),
        'fields'      => [
            'hasPaywall' => [
                'type'        => 'Boolean',
                'description' => __('Whether the content has a paywall.', 'fd-member'),
            ],
            'previewContent' => [
                'type'        => 'String',
                'description' => __('The preview content without paywall HTML.', 'fd-member'),
            ],
            'loginUrl' => [
                'type'        => 'String',
                'description' => __('URL for user login.', 'fd-member'),
            ],
            'registerUrl' => [
                'type'        => 'String',
                'description' => __('URL for user registration.', 'fd-member'),
            ],
            'upgradeUrl' => [
                'type'        => 'String',
                'description' => __('URL for membership upgrade.', 'fd-member'),
            ],
            'message' => [
                'type'        => 'String',
                'description' => __('Paywall message text.', 'fd-member'),
            ],
            'isLoggedIn' => [
                'type'        => 'Boolean',
                'description' => __('Whether the current user is logged in.', 'fd-member'),
            ],
        ],
    ]);

    // 注册全局设置字段
    register_graphql_object_type('FdMemberSettings', [
        'description' => __('Global settings for the FD Member plugin.', 'fd-member'),
        'fields'      => [
            'paywallVariant' => [
                'type'        => 'String',
                'description' => __('The global default style for paywalls (e.g., "default" or "compact").', 'fd-member'),
            ],
        ],
    ]);

    register_graphql_field('RootQuery', 'fdMemberSettings', [
        'type'        => 'FdMemberSettings',
        'description' => __('Get global settings for the FD Member plugin.', 'fd-member'),
        'resolve'     => function () {
            return [
                'paywallVariant' => get_option('fd_member_paywall_variant', 'default'),
            ];
        },
    ]);
}, 20);

/**
 * 添加创建解锁文章订单的GraphQL Mutation
 */
add_action('graphql_register_types', function () {
    register_graphql_mutation('createUnlockOrder', [
        'inputFields' => [
            'postId' => [
                'type' => ['non_null' => 'ID'],
                'description' => __('The ID of the post to unlock.', 'fd-member'),
            ],
            'paymentMethod' => [
                'type' => 'String',
                'description' => __('Payment method id (e.g. alipay, wxpay, wallet, points).', 'fd-member'),
            ],
        ],
        'outputFields' => [
            'orderId' => [
                'type' => 'ID',
                'description' => __('The ID of the created order.', 'fd-member'),
                'resolve' => function ($payload) {
                    return $payload['order_id'] ?? null;
                }
            ],
            'success' => [
                'type' => 'Boolean',
                'description' => __('Whether the order was successfully created.', 'fd-member'),
                'resolve' => function ($payload) {
                    return $payload['success'] ?? false;
                }
            ],
            'message' => [
                'type' => 'String',
                'description' => __('Status message about the order creation.', 'fd-member'),
                'resolve' => function ($payload) {
                    return $payload['message'] ?? '';
                }
            ],
        ],
        'mutateAndGetPayload' => function ($input) {
            // 验证用户是否登录
            if (!is_user_logged_in()) {
                throw new \GraphQL\Error\UserError(__('You must be logged in to unlock posts.', 'fd-member'));
            }
            
            $user_id = get_current_user_id();
            $post_id = absint($input['postId']);
            
            // 验证文章是否存在
            $post = get_post($post_id);
            if (!$post || $post->post_status !== 'publish') {
                throw new \GraphQL\Error\UserError(__('Invalid or unpublished post.', 'fd-member'));
            }
            
            // 验证文章是否需要解锁（非公开）
            $required_level_id = intval(get_post_meta($post_id, FD_POST_ACCESS_LEVEL_META_KEY, true));
            if ($required_level_id === 0) {
                throw new \GraphQL\Error\UserError(__('This post is already public and does not need to be unlocked.', 'fd-member'));
            }
            
            // 验证用户是否已经解锁该文章
            if (fd_member_has_user_unlocked_post($user_id, $post_id)) {
                throw new \GraphQL\Error\UserError(__('You have already unlocked this post.', 'fd-member'));
            }
            
            // 验证文章是否设置了解锁价格
            $unlock_price = floatval(get_post_meta($post_id, FD_POST_UNLOCK_PRICE_META_KEY, true));
            if ($unlock_price <= 0) {
                throw new \GraphQL\Error\UserError(__('This post does not support individual unlocking.', 'fd-member'));
            }
            
            // 检查用户是否已经有权限访问（通过会员等级）
            $user_level_info = fd_member_get_user_member_level_cached($user_id);
            $user_priority = $user_level_info ? intval($user_level_info['priority']) : -1;
            
            $required_level_info = fd_member_get_member_level($required_level_id);
            $required_priority = $required_level_info ? intval($required_level_info['priority']) : 0;
            
            if ($user_priority >= $required_priority) {
                throw new \GraphQL\Error\UserError(__('You already have access to this post through your membership level.', 'fd-member'));
            }
            
            // 创建订单
            $post_title = get_the_title($post_id);
            $order_title = sprintf(__('Unlock post: %s', 'fd-member'), $post_title);
            
            // 确定支付方式，默认使用 alipay，可由输入覆盖
            $payment_method = isset($input['paymentMethod']) && $input['paymentMethod']
                ? sanitize_text_field($input['paymentMethod'])
                : 'alipay';
            // 构建元数据 JSON
            $metadata = wp_json_encode([
                'order_type' => 'post_unlock',
                'post_id'   => $post_id,
                'callbackUrl' => wp_get_referer() ? esc_url_raw(wp_get_referer()) : ''
            ]);

            // 调用支付系统创建订单：title, amount, user_id, payment_method, description, product_type, product_id, metadata
            $order_id = fd_payment_create_order(
                $order_title,
                $unlock_price,
                $user_id,
                $payment_method,
                sprintf(__('Unlock access to post: %s', 'fd-member'), $post_title),
                'post_unlock',
                $post_id,
                $metadata
            );
            
            if (!$order_id) {
                throw new \GraphQL\Error\UserError(__('Failed to create order.', 'fd-member'));
            }
            
            return [
                'success' => true,
                'message' => __('Order created successfully.', 'fd-member'),
                'order_id' => $order_id
            ];
        }
    ]);
}, 20); 