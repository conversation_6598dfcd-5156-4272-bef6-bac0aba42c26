<?php
/**
 * Reactions GraphQL API
 *
 * 注册通用的交互（Reaction）相关 GraphQL 类型、查询和变更。
 *
 * @package FD\Member
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// 确保 ReactionService 可用
require_once __DIR__ . '/class-reaction-service.php';


add_action( 'graphql_register_types', function () {

    // 1. 注册交互类型枚举 (ReactionTypeEnum)
    register_graphql_enum_type( 'ReactionTypeEnum', [
        'description' => __( '交互类型', 'fd-member' ),
        'values'      => [
            'LIKE'      => [ 'value' => 'like' ],
            'BOOKMARK'  => [ 'value' => 'bookmark' ],
            'RECOMMEND' => [ 'value' => 'recommend' ],
        ],
    ] );

    // 2. 在 ContentNode 接口上注入通用字段
    register_graphql_field( 'ContentNode', 'reactionCount', [
        'type'        => 'Int',
        'description' => __( '获取指定类型的交互总数', 'fd-member' ),
        'args'        => [
            'type' => [ 'type' => [ 'non_null' => 'ReactionTypeEnum' ] ],
        ],
        'resolve'     => function ( $source, $args ) {
            return ReactionService::count( $args['type'], $source->ID );
        },
    ] );

    register_graphql_field( 'ContentNode', 'userHasReacted', [
        'type'        => 'Boolean',
        'description' => __( '当前用户是否已执行指定类型的交互', 'fd-member' ),
        'args'        => [
            'type' => [ 'type' => [ 'non_null' => 'ReactionTypeEnum' ] ],
        ],
        'resolve'     => function ( $source, $args ) {
            $user_id = get_current_user_id();
            return $user_id ? ReactionService::has( $args['type'], $user_id, $source->ID ) : false;
        },
    ] );

    // 3. 定义通用变更 (addReaction)
    register_graphql_mutation( 'addReaction', [
        'inputFields'         => [
            'type'    => [ 'type' => [ 'non_null' => 'ReactionTypeEnum' ] ],
            'postId'  => [ 'type' => [ 'non_null' => 'ID' ] ],
        ],
        'outputFields'        => [
            'success' => [ 'type' => 'Boolean' ],
            'message' => [ 'type' => 'String' ],
        ],
        'mutateAndGetPayload' => function ( $input ) {
            if ( ! is_user_logged_in() ) {
                throw new \GraphQL\Error\UserError( __( '请先登录', 'fd-member' ) );
            }
            // 解析 postId：既兼容全局 ID，也兼容直接传入数据库 ID
            $post_id = absint( $input['postId'] );
            if ( 0 === $post_id ) {
                $decoded = base64_decode( $input['postId'] );
                if ( $decoded ) {
                    $parts   = explode( ':', $decoded );
                    $post_id = absint( end( $parts ) );
                }
            }
            $result  = ReactionService::set( $input['type'], get_current_user_id(), $post_id, 1 );

            return [
                'success' => (bool) $result,
                'message' => $result ? __( '操作成功', 'fd-member' ) : __( '操作失败', 'fd-member' ),
            ];
        },
    ] );

    // 4. 定义通用变更 (removeReaction)
    register_graphql_mutation( 'removeReaction', [
        'inputFields'         => [
            'type'    => [ 'type' => [ 'non_null' => 'ReactionTypeEnum' ] ],
            'postId'  => [ 'type' => [ 'non_null' => 'ID' ] ],
        ],
        'outputFields'        => [
            'success' => [ 'type' => 'Boolean' ],
            'message' => [ 'type' => 'String' ],
        ],
        'mutateAndGetPayload' => function ( $input ) {
            if ( ! is_user_logged_in() ) {
                throw new \GraphQL\Error\UserError( __( '请先登录', 'fd-member' ) );
            }
            // 解析 postId：既兼容全局 ID，也兼容直接传入数据库 ID
            $post_id = absint( $input['postId'] );
            if ( 0 === $post_id ) {
                $decoded = base64_decode( $input['postId'] );
                if ( $decoded ) {
                    $parts   = explode( ':', $decoded );
                    $post_id = absint( end( $parts ) );
                }
            }
            $result  = ReactionService::set( $input['type'], get_current_user_id(), $post_id, 0 );

            return [
                'success' => (bool) $result,
                'message' => $result ? __( '操作成功', 'fd-member' ) : __( '操作失败', 'fd-member' ),
            ];
        },
    ] );

    // 5. 首先注册UserReactionsConnection类型
    register_graphql_object_type( 'UserReactionsConnection', [
        'description' => __( '用户反应连接类型', 'fd-member' ),
        'fields'      => [
            'nodes' => [
                'type'        => [ 'list_of' => 'Post' ],
                'description' => __( '文章节点列表', 'fd-member' ),
            ],
            'pageInfo' => [
                'type'        => 'WPPageInfo',
                'description' => __( '分页信息', 'fd-member' ),
            ],
        ],
    ] );

    // 6. 注册用户反应查询字段
    register_graphql_field( 'RootQuery', 'userReactions', [
        'type'        => 'UserReactionsConnection',
        'description' => __( '获取用户的反应列表', 'fd-member' ),
        'args'        => [
            'type' => [
                'type'        => [ 'non_null' => 'ReactionTypeEnum' ],
                'description' => __( '反应类型', 'fd-member' ),
            ],
            'first' => [
                'type'         => 'Int',
                'description'  => __( '获取数量', 'fd-member' ),
                'defaultValue' => 10,
            ],
            'after' => [
                'type'        => 'String',
                'description' => __( '游标（用于分页）', 'fd-member' ),
            ],
        ],
        'resolve' => function ( $root, $args ) {
            if ( ! is_user_logged_in() ) {
                throw new \GraphQL\Error\UserError( __( '请先登录', 'fd-member' ) );
            }

            $user_id = get_current_user_id();
            $type    = $args['type'];
            $first   = isset( $args['first'] ) ? absint( $args['first'] ) : 10;
            $after   = isset( $args['after'] ) ? $args['after'] : null;

            // 计算偏移量（简单的游标分页实现）
            $offset = 0;
            if ( $after ) {
                $offset = absint( base64_decode( $after ) );
            }

            // 获取文章ID列表
            $post_ids = ReactionService::get_user_reactions( $type, $user_id, $first + 1, $offset );

            // 检查是否有更多数据
            $has_next_page = count( $post_ids ) > $first;
            if ( $has_next_page ) {
                array_pop( $post_ids ); // 移除多获取的一个
            }

            // 转换为Post对象
            $posts = [];
            $context = \WPGraphQL::get_app_context();

            foreach ( $post_ids as $post_id ) {
                $post = get_post( $post_id );
                if ( $post && $post->post_status === 'publish' ) {
                    $resolved = \WPGraphQL\Data\DataSource::resolve_post_object( $post_id, $context );
                    if ( $resolved ) {
                        $posts[] = $resolved;
                    }
                }
            }

            // 构建分页信息
            $end_cursor = null;
            if ( $has_next_page && ! empty( $posts ) ) {
                $end_cursor = base64_encode( $offset + count( $posts ) );
            }

            return [
                'nodes'    => $posts,
                'pageInfo' => [
                    'hasNextPage'     => $has_next_page,
                    'hasPreviousPage' => $offset > 0,
                    'startCursor'     => ! empty( $posts ) ? base64_encode( $offset ) : null,
                    'endCursor'       => $end_cursor,
                ],
            ];
        },
    ] );

} );