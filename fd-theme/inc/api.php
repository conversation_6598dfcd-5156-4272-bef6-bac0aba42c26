<?php
/**
 * REST API 基础支持
 */

if (!defined('ABSPATH')) {
    exit; // 禁止直接访问
}

/**
 * 添加CORS头
 * 确保前端应用可以访问WordPress REST API和GraphQL API
 */
function fd_add_cors_headers() {
    // 允许的域名列表
    $allowed_origins = array(
        'https://www.futuredecade.com',
        'https://futuredecade.com',
        'http://localhost:3000' // 开发环境
    );

    // 获取当前请求的Origin
    $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';

    // 如果Origin在允许列表中，则设置对应的CORS头
    if (in_array($origin, $allowed_origins)) {
        header("Access-Control-Allow-Origin: $origin");
        header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
        header("Access-Control-Allow-Credentials: true");
        header("Access-Control-Allow-Headers: Authorization, Content-Type, X-WP-Nonce");
        
        // 对于预检请求，直接返回200状态码
        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            status_header(200);
            exit;
        }
    }
}

// 应用于REST API
add_action('rest_api_init', 'fd_add_cors_headers');

// 应用于所有请求（包括GraphQL）
add_action('init', 'fd_add_cors_headers');

// 专门针对GraphQL请求添加CORS头
add_action('graphql_init', 'fd_add_cors_headers'); 

/**
 * 注册获取 CPT 信息的 REST API 接口。
 * GET /wp-json/fd/v1/cpt-info/{type}
 * 返回 JSON: {
 *   isValid: bool,
 *   listFragment: string,
 *   cacheTag: string
 * }
 */
function fd_register_cpt_info_endpoint() {
    register_rest_route('fd/v1', '/cpt-info/(?P<type>[a-zA-Z0-9_-]+)', [
        'methods'  => 'GET',
        'callback' => 'fd_get_cpt_info',
        'permission_callback' => '__return_true', // 公开接口，仅返回结构化数据
    ]);
}
add_action('rest_api_init', 'fd_register_cpt_info_endpoint');

/**
 * 将 slug 转为 GraphQL 类型名，例如 note -> Note, book-list -> BookList
 */
function fd_to_graphql_type_name($slug) {
    $parts = explode('-', $slug);
    $parts = array_map(function ($p) { return ucfirst($p); }, $parts);
    return implode('', $parts);
}

/**
 * 构建 Fragment 字符串（简化版，只包含常用字段 + 所有 ACF 字段）
 */
function fd_build_fragment($type_slug, $acf_container_field_name, $acf_fields, $graphql_type_name) {
    $fragment_name = $graphql_type_name . 'ListFields';

    // 构建 ACF 字段行
    $acf_lines = '';
    foreach ($acf_fields as $field) {
        $acf_lines .= "        {$field}\n";
    }

    $fragment  = "fragment {$fragment_name} on {$graphql_type_name} {\n";
    $fragment .= "  id\n  databaseId\n  title\n  slug\n  shortUuid\n  date\n  excerpt(format: RENDERED)\n";

    // 特色图片
    $fragment .= "  featuredImage {\n    node {\n      sourceUrl\n      altText\n    }\n  }\n";

    // 作者信息
    $fragment .= "  author {\n    node {\n      id\n      name\n      slug\n      nickname\n      avatar {\n        url\n      }\n    }\n  }\n";

    // 反应数据（点赞、收藏、推荐）
    $fragment .= "  likeCount: reactionCount(type: LIKE)\n";
    $fragment .= "  userHasLiked: userHasReacted(type: LIKE)\n";
    $fragment .= "  bookmarkCount: reactionCount(type: BOOKMARK)\n";
    $fragment .= "  userHasBookmarked: userHasReacted(type: BOOKMARK)\n";
    $fragment .= "  recommendCount: reactionCount(type: RECOMMEND)\n";
    $fragment .= "  userHasRecommended: userHasReacted(type: RECOMMEND)\n";
    $fragment .= "  commentCount\n";

    // ACF 字段
    if ($acf_container_field_name) {
        $fragment .= "  {$acf_container_field_name} {\n{$acf_lines}  }\n";
    }
    $fragment .= "}\n";

    return $fragment;
}

/**
 * 接口实现回调
 */
function fd_get_cpt_info($request) {
    $type_slug = sanitize_key($request['type']);
    if (empty($type_slug)) {
        return rest_ensure_response(['isValid' => false]);
    }

    $transient_key = 'fd_cpt_info_' . $type_slug;
    $cached        = get_transient($transient_key);
    if ($cached) {
        return rest_ensure_response(json_decode($cached, true));
    }

    // 1) 执行自省查询，找出 ACF container 字段及其内部字段名
    $graphql_endpoint = home_url('/graphql');
    $graphql_type_name = fd_to_graphql_type_name($type_slug);

    // 查询类型字段
    $introspection_query = 'query IntrospectType($typeName: String!) { __type(name: $typeName) { fields { name type { name kind } } } }';
    $response = wp_remote_post($graphql_endpoint, [
        'headers' => ['Content-Type' => 'application/json'],
        'body'    => wp_json_encode(['query' => $introspection_query, 'variables' => ['typeName' => $graphql_type_name]]),
        'timeout' => 10,
    ]);

    if (is_wp_error($response)) {
        return rest_ensure_response(['isValid' => false, 'error' => $response->get_error_message()]);
    }

    $body = json_decode(wp_remote_retrieve_body($response), true);
    if (!isset($body['data']['__type']['fields'])) {
        return rest_ensure_response(['isValid' => false]);
    }

    $fields = $body['data']['__type']['fields'];
    $acf_container_field_name = null;
    foreach ($fields as $field) {
        if (str_ends_with(strtolower($field['name']), 'fields') && $field['type']['kind'] === 'OBJECT') {
            $acf_container_field_name = $field['name'];
            $acf_container_type_name  = $field['type']['name'];
            break;
        }
    }

    $acf_field_names = [];
    if ($acf_container_field_name && $acf_container_type_name) {
        // 查询container内部字段
        $container_query = 'query IntrospectAcfContainer($typeName: String!) { __type(name: $typeName) { fields { name } } }';
        $resp2 = wp_remote_post($graphql_endpoint, [
            'headers' => ['Content-Type' => 'application/json'],
            'body'    => wp_json_encode(['query' => $container_query, 'variables' => ['typeName' => $acf_container_type_name]]),
            'timeout' => 10,
        ]);
        if (!is_wp_error($resp2)) {
            $body2 = json_decode(wp_remote_retrieve_body($resp2), true);
            $inner_fields = $body2['data']['__type']['fields'] ?? [];
            foreach ($inner_fields as $f) {
                $acf_field_names[] = $f['name'];
            }
        }
    }

    // 构建Fragment
    $fragment = fd_build_fragment($type_slug, $acf_container_field_name, $acf_field_names, $graphql_type_name);

    $result = [
        'isValid'      => true,
        'cacheTag'     => 'cpt-list:' . $type_slug,
        'listFragment' => $fragment,
    ];

    // 缓存12小时
    set_transient($transient_key, wp_json_encode($result), 12 * HOUR_IN_SECONDS);

    return rest_ensure_response($result);
}

/**
 * 当 ACF 字段组发生变动时，清空相关缓存
 */
add_action('acf/update_field_group', function ($group) {
    // 最简单策略：直接删除所有 fd_cpt_info_* 缓存
    global $wpdb;
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_fd_cpt_info_%'");
});

/**
 * 手动清除 CPT Fragment 缓存的 REST API 接口
 * POST /wp-json/fd/v1/clear-cpt-cache
 */
add_action('rest_api_init', function() {
    register_rest_route('fd/v1', '/clear-cpt-cache', [
        'methods'  => 'POST',
        'callback' => 'fd_clear_cpt_cache',
        'permission_callback' => function() {
            return current_user_can('manage_options'); // 只有管理员可以清除缓存
        },
    ]);
});

function fd_clear_cpt_cache($request) {
    global $wpdb;
    $deleted = $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_fd_cpt_info_%'");

    return rest_ensure_response([
        'success' => true,
        'message' => "已清除 {$deleted} 个 CPT Fragment 缓存",
        'deleted_count' => $deleted
    ]);
}