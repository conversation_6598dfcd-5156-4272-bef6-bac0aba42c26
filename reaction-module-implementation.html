<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <title>Future Decade&nbsp;– 点赞 / 收藏 / 推荐统一 Reaction 模块完整实现文档</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif; line-height: 1.6; margin: 0; padding: 0 1rem 3rem; color: #333; }
    h1, h2, h3, h4 { color: #111; margin-top: 2rem; }
    h1 { font-size: 2rem; }
    h2 { font-size: 1.5rem; border-bottom: 2px solid #eee; padding-bottom: .4rem; }
    code { background: #f5f5f5; padding: .1rem .3rem; border-radius: 4px; }
    pre { background: #f5f5f5; padding: 1rem; overflow: auto; border-radius: 6px; }
    table { border-collapse: collapse; margin: 1rem 0; width: 100%; }
    th, td { border: 1px solid #ddd; padding: .5rem; text-align: left; }
    th { background: #fafafa; }
    .file-path { font-weight: bold; color: #d6336c; }
    .section-link { font-size: .85rem; margin-left: .3rem; color: #0d6efd; text-decoration: none; }
  </style>
</head>
<body>
<h1>Future Decade&nbsp;– Reaction 模块（点赞 / 收藏 / 推荐）完整实现文档</h1>
<p>本文件详细记录了在 <code>fd-member</code>（后端插件）与 <code>fd-frontend</code>（Next.js 前端）中，如何将原先三个相似的功能 —— <strong>点赞 (like)</strong>、<strong>收藏 (bookmark)</strong>、<strong>推荐 (recommend)</strong> —— 统一抽象为 <em>Reaction</em> 模块的全过程，包括数据库设计、PHP 业务实现、GraphQL 暴露及 TypeScript/React 前端交互。</p>
<hr/>

<h2 id="overview">1.&nbsp;整体架构概览<a href="#overview" class="section-link">#</a></h2>
<p>
  <strong>目标：</strong>消除三套重复代码，统一数据表与服务层，提供统一的扩展点（Action &amp; Filter）与 GraphQL API，同时保持向旧代码的兼容性。整体架构如下：</p>
<ul>
  <li><strong>Database</strong>：新建 <code>wp_reactions</code> 表，存储所有反应记录。</li>
  <li><strong>Service Layer</strong>：<code>ReactionService</code> (PHP class) 负责增删查计数等核心逻辑，并通过 WordPress Action 触发事件。</li>
  <li><strong>GraphQL</strong>：<code>reactions-graphql.php</code> 注册查询/字段/枚举与 <code>addReaction</code>、<code>removeReaction</code> Mutations。</li>
  <li><strong>Notification &amp; Stats</strong>：监听 <code>fd_reaction_added</code>/<code>fd_reaction_updated</code> 实现消息推送与缓存。</li>
  <li><strong>Frontend</strong>：通用 <code>ReactionButton</code> + Apollo Client + TypeScript Enum；所有模板统一使用。</li>
</ul>

<h2 id="db">2.&nbsp;数据库设计<a href="#db" class="section-link">#</a></h2>
<p>文件：<span class="file-path">fd-member/includes/reactions/reaction-core.php</span></p>
<pre><code class="language-sql">CREATE TABLE `wp_reactions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(20) NOT NULL,           -- LIKE / BOOKMARK / RECOMMEND
  `user_id` bigint(20) unsigned NOT NULL,
  `post_id` bigint(20) unsigned NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_post_type` (`user_id`,`post_id`,`type`),
  KEY `post_type` (`post_id`,`type`)
) DEFAULT CHARSET=utf8mb4;</code></pre>
<p>通过 <code>register_activation_hook</code> 在插件激活时自动创建 / 升级表结构。</p>

<h2 id="php-service">3.&nbsp;后端服务层实现<a href="#php-service" class="section-link">#</a></h2>
<p>文件：<span class="file-path">fd-member/includes/reactions/class-reaction-service.php</span></p>
<pre><code class="language-php">class ReactionService {
  public static function set( $type, $user_id, $post_id, $status = 1 ) {
    global $wpdb;
    $table = $wpdb-&gt;prefix . 'reactions';

    $existing = $wpdb-&gt;get_row( $wpdb-&gt;prepare(
      "SELECT * FROM {$table} WHERE user_id = %d AND post_id = %d AND type = %s",
      $user_id, $post_id, $type
    ));

    $data = [
      'user_id'    =&gt; $user_id,
      'post_id'    =&gt; $post_id,
      'type'       =&gt; $type,
      'status'     =&gt; $status,
      'updated_at' =&gt; current_time( 'mysql', true ),
    ];

    if ( $existing ) {
      $wpdb-&gt;update( $table, $data, [ 'id' =&gt; $existing-&gt;id ] );
      if ( (int) $existing-&gt;status !== $status ) {
        do_action( 'fd_reaction_updated', $type, $user_id, $post_id, $status );
      }
      return $existing-&gt;id;
    }

    $data['created_at'] = $data['updated_at'];
    if ( false !== $wpdb-&gt;insert( $table, $data ) ) {
      do_action( 'fd_reaction_added', $type, $user_id, $post_id, $status );
      return $wpdb-&gt;insert_id;
    }
    return false;
  }

  public static function get_status( $type, $user_id, $post_id ) {
    global $wpdb;
    return (int) $wpdb-&gt;get_var( $wpdb-&gt;prepare(
      "SELECT status FROM {$wpdb-&gt;prefix}reactions WHERE user_id=%d AND post_id=%d AND type=%s",
      $user_id, $post_id, $type
    ));
  }

  public static function count( $type, $post_id, $status = 1 ) {
    global $wpdb;
    return (int) $wpdb-&gt;get_var( $wpdb-&gt;prepare(
      "SELECT COUNT(*) FROM {$wpdb-&gt;prefix}reactions WHERE post_id=%d AND type=%s AND status=%d",
      $post_id, $type, $status
    ));
  }
}
</code></pre>

<h3>3.1&nbsp;事件钩子</h3>
<ul>
  <li><code>fd_reaction_added</code>(string <em>$type</em>, int <em>$user_id</em>, int <em>$post_id</em>, int <em>$status</em>)</li>
  <li><code>fd_reaction_updated</code>(同上)</li>
</ul>
<p>供通知、缓存、统计等其它模块监听。</p>

<h2 id="graphql">4.&nbsp;GraphQL 接口<a href="#graphql" class="section-link">#</a></h2>
<p>文件：<span class="file-path">fd-member/includes/reactions/reactions-graphql.php</span></p>
<h3>4.1&nbsp;枚举</h3>
<pre><code class="language-php">register_graphql_enum_type( 'ReactionTypeEnum', [
  'description' =&gt; 'Reaction type',
  'values'      =&gt; [
    'LIKE'      =&gt; [ 'value' =&gt; 'LIKE' ],
    'BOOKMARK'  =&gt; [ 'value' =&gt; 'BOOKMARK' ],
    'RECOMMEND' =&gt; [ 'value' =&gt; 'RECOMMEND' ],
  ],
] );</code></pre>

<h3>4.2&nbsp;字段</h3>
<pre><code class="language-php">// 在 ContentNode 接口统一注册
register_graphql_field( 'ContentNode', 'reactionCount', [
  'type'        =&gt; 'Int',
  'args'        =&gt; [ 'type' =&gt; [ 'type' =&gt; 'ReactionTypeEnum' ] ],
  'resolve'     =&gt; function( $source, $args ) {
    return ReactionService::count( $args['type'], $source-&gt;databaseId );
  },
] );

register_graphql_field( 'ContentNode', 'userHasReacted', [
  'type'    =&gt; 'Boolean',
  'args'    =&gt; [ 'type' =&gt; [ 'type' =&gt; 'ReactionTypeEnum' ] ],
  'resolve' =&gt; function( $source, $args, $context ) {
    $user_id = get_current_user_id();
    return $user_id ? ReactionService::get_status( $args['type'], $user_id, $source-&gt;databaseId ) : false;
  },
] );</code></pre>

<h3>4.3&nbsp;Mutations</h3>
<pre><code class="language-php">register_graphql_mutation( 'addReaction', [
  'inputFields'  =&gt; [
    'type'   =&gt; [ 'type' =&gt; 'ReactionTypeEnum' ],
    'postId' =&gt; [ 'type' =&gt; 'ID' ],
  ],
  'mutateAndGetPayload' =&gt; function( $input ) {
    $post_id = fd_decode_global_or_db_id( $input['postId'] );
    ReactionService::set( $input['type'], get_current_user_id(), $post_id, 1 );
    return [ 'success' =&gt; true ];
  },
  'outputFields' =&gt; [ 'success' =&gt; [ 'type' =&gt; 'Boolean' ] ],
] );

register_graphql_mutation( 'removeReaction', [
  'inputFields'  =&gt; [
    'type'   =&gt; [ 'type' =&gt; 'ReactionTypeEnum' ],
    'postId' =&gt; [ 'type' =&gt; 'ID' ],
  ],
  'mutateAndGetPayload' =&gt; function( $input ) {
    $post_id = fd_decode_global_or_db_id( $input['postId'] );
    ReactionService::set( $input['type'], get_current_user_id(), $post_id, 0 );
    return [ 'success' =&gt; true ];
  },
  'outputFields' =&gt; [ 'success' =&gt; [ 'type' =&gt; 'Boolean' ] ],
] );</code></pre>
<p><code>fd_decode_global_or_db_id</code> 为手写的 <code>base64_decode</code> 助手，可兼容 WPGraphQL 的全局 ID 与纯数据库 ID。</p>

<h2 id="frontend">5.&nbsp;前端实现<a href="#frontend" class="section-link">#</a></h2>
<h3>5.1&nbsp;TypeScript 类型</h3>
<p>文件：<span class="file-path">fd-frontend/src/types/Reaction.ts</span></p>
<pre><code class="language-ts">export enum ReactionType {
  Like = 'LIKE',
  Bookmark = 'BOOKMARK',
  Recommend = 'RECOMMEND',
}</code></pre>
<p>所有 <code>Post</code> 类型新增字段：</p>
<table><thead><tr><th>字段</th><th>类型</th><th>来源</th></tr></thead><tbody>
<tr><td>likeCount</td><td>number</td><td><code>reactionCount(type: LIKE)</code></td></tr>
<tr><td>userHasLiked</td><td>boolean</td><td><code>userHasReacted(type: LIKE)</code></td></tr>
<tr><td>bookmarkCount</td><td>number</td><td>同上</td></tr>
<tr><td>userHasBookmarked</td><td>boolean</td><td>同上</td></tr>
<tr><td>recommendCount</td><td>number</td><td>同上</td></tr>
<tr><td>userHasRecommended</td><td>boolean</td><td>同上</td></tr>
</tbody></table>

<h3>5.2&nbsp;GraphQL 片段 &amp; 查询</h3>
<p>文件：<span class="file-path">fd-frontend/src/lib/graphql/fragments.ts</span></p>
<pre><code class="language-ts">export const REACTION_FIELDS_FRAGMENT = gql`
  fragment ReactionFields on ContentNode {
    likeCount: reactionCount(type: LIKE)
    userHasLiked: userHasReacted(type: LIKE)
    bookmarkCount: reactionCount(type: BOOKMARK)
    userHasBookmarked: userHasReacted(type: BOOKMARK)
    recommendCount: reactionCount(type: RECOMMEND)
    userHasRecommended: userHasReacted(type: RECOMMEND)
  }
`;</code></pre>
<p><strong>Mutation</strong>：</p>
<pre><code class="language-ts">export const REACTION_MUTATION = gql`
  mutation Reaction($type: ReactionTypeEnum!, $postId: ID!) {
    addReaction(input: { type: $type, postId: $postId }) { success }
  }
`;

export const REMOVE_REACTION_MUTATION = gql`
  mutation RemoveReaction($type: ReactionTypeEnum!, $postId: ID!) {
    removeReaction(input: { type: $type, postId: $postId }) { success }
  }
`;</code></pre>

<h3>5.3&nbsp;React 组件</h3>
<p>文件：<span class="file-path">fd-frontend/src/components/post/ReactionButton.tsx</span></p>
<pre><code class="language-tsx">const ReactionButton: React.FC&lt;ReactionButtonProps&gt; = ({
  postId,
  type,
  icon,
  initialState,
  initialCount,
  successMessage,
  undoMessage,
  activeClass,
  inactiveClass,
}) =&gt; {
  const [state, setState] = useState(initialState);
  const [count, setCount] = useState(initialCount);

  const [reactMutation] = useMutation(state ? REMOVE_REACTION_MUTATION : REACTION_MUTATION);

  const toggle = async () =&gt; {
    setState(!state);
    setCount(prev =&gt; (state ? prev - 1 : prev + 1)); // Optimistic UI
    try {
      await reactMutation({ variables: { type, postId } });
      toast.success(state ? undoMessage : successMessage);
    } catch (e) {
      // 回滚
      setState(state);
      setCount(prev =&gt; (state ? prev + 1 : prev - 1));
      toast.error('操作失败');
    }
  };

  return (
    <button onClick={toggle} className={`px-3 py-1 rounded ${state ? activeClass : inactiveClass}`}>
      {icon}
      <span>{count}</span>
    </button>
  );
};</code></pre>

<h3>5.4&nbsp;模板使用示例</h3>
<p>文章与自定义类型模板统一使用：</p>
<pre><code class="language-tsx"><ReactionButton
  postId={post.id}
  type={ReactionType.Like}
  icon={<HiOutlineHeart className="w-5 h-5 mr-2" />}
  initialCount={post.likeCount}
  initialState={post.userHasLiked}
  activeClass="bg-rose-500 text-white"
  inactiveClass="bg-gray-200 text-gray-700"
  successMessage="感谢点赞！"
  undoMessage="已取消点赞"
/>
</code></pre>

<h2 id="flow">6.&nbsp;交互流程示意<a href="#flow" class="section-link">#</a></h2>
<pre><code class="language-mermaid">sequenceDiagram
  participant UI as ReactionButton
  participant FE as Apollo Client
  participant GQL as WPGraphQL
  participant PHP as ReactionService
  UI-&gt;&gt;FE: click toggle()
  FE-&gt;&gt;GQL: addReaction(type, postId)
  GQL--&gt;&gt;PHP: mutateAndGetPayload()
  PHP--&gt;DB: INSERT / UPDATE wp_reactions
  PHP--&gt;&gt;GQL: { success: true }
  GQL--&gt;&gt;FE: response
  FE-->>UI: optimistic already applied
  Note right of PHP: 触发 fd_reaction_added <br/>| fd_reaction_updated
</code></pre>

<h2 id="ext">7.&nbsp;扩展性与向后兼容<a href="#ext" class="section-link">#</a></h2>
<ul>
  <li>如需新增「点踩」等其它 Reaction，只需在枚举中加入类型并扩展前端枚举/按钮即可。</li>
  <li>旧函数别名 <code>fd_member_get_post_likes_count</code> 等在 <code>reaction-stats.php</code> 中保留，确保模板不报错。</li>
  <li>数据库唯一键 (user_id + post_id + type) 保证相同类型单一记录，可安全扩展。</li>
</ul>

<h2 id="migration">8.&nbsp;迁移注意事项<a href="#migration" class="section-link">#</a></h2>
<ol>
  <li>部署前运行一次插件<strong>激活</strong>或 CLI 命令 <code>wp plugin activate fd-member</code> 以创建新表。</li>
  <li>将旧表数据 (likes, bookmarks, recommends) 迁入 <code>wp_reactions</code> 并填充 <code>type</code> 字段，可使用 SQL：<br/>
    <code>INSERT INTO wp_reactions(type,user_id,post_id,status,created_at,updated_at) SELECT 'LIKE',user_id,post_id,status,created_at,updated_at FROM wp_likes;</code>
  </li>
  <li>删除旧代码目录 <code>includes/likes</code> 等，防止重复加载。</li>
</ol>

<hr/>
<p><em>文档版本：2024-07-28</em></p>
</body>
</html> 