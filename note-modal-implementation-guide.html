<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>功能实现文档：笔记详情页模态框 (Modal)</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f9f9f9;
    }
    h1, h2, h3, h4 {
      color: #2c3e50;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    code {
      font-family: "SFMono-Regular", <PERSON>solas, "Liberation Mono", Menlo, Courier, monospace;
      background-color: #eee;
      padding: 0.2em 0.4em;
      border-radius: 3px;
      font-size: 85%;
    }
    pre {
      background-color: #f6f8fa;
      padding: 16px;
      overflow: auto;
      border-radius: 6px;
    }
    .container {
      background-color: #fff;
      padding: 25px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .summary {
      background-color: #eef7ff;
      border-left: 4px solid #3498db;
      padding: 10px 15px;
      margin: 20px 0;
    }
    .warning {
      background-color: #fffbe6;
      border-left: 4px solid #f0b429;
      padding: 10px 15px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>功能实现文档：笔记详情页模态框 (Modal)</h1>
    <p><strong>目标：</strong>当用户在笔记列表页 (<code>/note</code>) 点击一个笔记时，以模态框（Modal）的形式在当前页打开笔记详情，而不是跳转到新页面。同时，直接访问笔记URL应显示完整的独立页面。</p>
    
    <div class="summary">
      <h4>最终实现方案总结</h4>
      <p>我们采用了一种结合 <strong>别名页面路由</strong> 和 <strong>精确路径拦截路由</strong> 的健壮方案，该方案最符合 Next.js App Router 的设计哲学，彻底解决了直接访问404和服务端渲染（SSR）的路由上下文问题。</p>
    </div>

    <h2>一、核心实现组件</h2>
    
    <h3>1. 别名页面 (Alias Pages)</h3>
    <p>这是解决直接访问URL时404问题的关键。我们为 <code>/note</code> 路径创建了实际的页面文件，使其在文件系统中有对应的入口。</p>
    <ul>
      <li>
        <strong>列表页别名:</strong>
        <code>fd-frontend/src/app/note/page.tsx</code>
        <p>此文件负责响应 <code>/note</code> 路由，它本身不包含复杂逻辑，而是直接导入并渲染通用的自定义类型列表页组件，同时硬编码 <code>type='note'</code>。</p>
      </li>
      <li>
        <strong>详情页别名:</strong>
        <code>fd-frontend/src/app/note/[uuid]/[slug]/page.tsx</code>
        <p>此文件负责响应 <code>/note/[uuid]/[slug]</code> 路由，同样，它导入并渲染通用的详情页组件，并硬编码 <code>type='note'</code>。它也负责重新导出元数据生成函数 (<code>generateMetadata</code>)。</p>
      </li>
    </ul>

    <h3>2. 拦截路由 (Intercepting Route)</h3>
    <p>这是实现模态框效果的核心。我们创建了一个拦截器，专门捕获用户在应用内导航到笔记详情页的行为。</p>
    <ul>
      <li>
        <strong>拦截器路径:</strong>
        <code>fd-frontend/src/app/@modal/(.)/note/[uuid]/[slug]/page.tsx</code>
        <p><strong>关键点：</strong></p>
        <ul>
          <li><strong><code>@modal</code>:</strong> 定义了一个并行的路由“插槽”。</li>
          <li><strong><code>(.)</code>:</strong> 语法表示拦截与 <code>@modal</code> 目录同级的路由。</li>
          <li><strong><code>/note/...</code>:</strong> 直接匹配用户看到的友好URL，而不是内部的 <code>/post-type/note/...</code> 路径，这消除了对URL重写的依赖，避免了许多潜在问题。</li>
        </ul>
      </li>
      <li>
        <strong>模态框组件 (<code>NoteModalPage</code>):</strong>
        <p>这是拦截器文件中的React组件，负责模态框的全部功能：</p>
        <ul>
            <li><strong>条件渲染:</strong> 使用 <code>useSearchParams</code> 钩子检查URL中是否存在 <code>?modal=true</code> 查询参数。只有当该参数存在时，才渲染模态框，否则返回 <code>null</code>。</li>
            <li><strong>数据获取:</strong> 在客户端使用 Apollo Client 的 <code>useQuery</code> 钩子获取笔记的详细数据，避免了在客户端组件中调用服务器端代码的问题。</li>
            <li><strong>UI & 交互:</strong> 包含背景遮罩、关闭按钮（响应点击和 'Esc' 键）、以及小红书风格的左右两栏布局。</li>
        </ul>
      </li>
    </ul>
    
    <h3>3. URL 查询参数标记</h3>
    <p>为了让拦截器能够区分“应用内点击”和“直接访问”，我们在列表页的链接中加入了标记。</p>
    <ul>
      <li>
        <strong>文件:</strong> <code>fd-frontend/src/components/CustomTypeViews/NotesView.tsx</code>
        <p>在此文件中，所有笔记卡片的 <code>&lt;Link&gt;</code> 组件的 <code>href</code> 属性都被附加了 <code>?modal=true</code> 查询参数。</p>
      </li>
    </ul>

    <h3>4. 主布局修改</h3>
    <p>为了让 <code>@modal</code> 插槽能够被渲染，我们对主布局文件进行了微小的修改。</p>
    <ul>
      <li>
        <strong>文件:</strong> <code>fd-frontend/src/app/layout.tsx</code>
        <p><code>RootLayout</code> 组件现在接收一个 <code>modal</code> 属性，并将其渲染在 <code>{children}</code> 旁边。</p>
      </li>
    </ul>

    <h2>二、配置清理</h2>
    <p>为了实现上述方案，我们移除了之前尝试中添加的、现在已不再需要的配置，使项目保持整洁。</p>
    <ul>
      <li><strong><code>next.config.js</code>:</strong> 移除了所有与 <code>note</code> 相关的 <code>rewrites</code> 规则。</li>
      <li><strong><code>middleware.ts</code>:</strong> 移除了所有为 <code>note</code> 添加的特殊重写或重定向规则。</li>
    </ul>

    <div class="warning">
      <h4>关键经验总结</h4>
      <p>在Next.js App Router中，当URL重写（Rewrites）和拦截路由（Intercepting Routes）结合使用时，行为可能变得复杂。最稳健的原则是：<strong>一个可被直接访问的URL路径，必须在 <code>app/</code> 目录下有一个与之对应的文件系统路径</strong>。我们的“别名页面”方案正是遵循了这一核心原则，从而一劳永逸地解决了SSR时的404问题。</p>
    </div>

    <h2>四、小红书风格UI优化需求分析</h2>

    <div class="warning">
      <h4>基于小红书界面的缺失元素分析</h4>
      <p>对比小红书的笔记详情页面，我们当前的Modal实现缺少以下关键元素和功能：</p>
    </div>

    <h3>1. 左侧主内容区域优化需求</h3>
    <ul>
      <li><strong>图片展示优化：</strong>
        <ul>
          <li>需要支持大图全屏展示，图片应占据左侧主要视觉空间</li>
          <li>图片上需要叠加半透明的标题文字（白色文字，黑色半透明背景）</li>
          <li>支持图片轮播（如果有多张图片）</li>
        </ul>
      </li>
      <li><strong>底部操作栏：</strong>
        <ul>
          <li>点赞按钮及数量显示</li>
          <li>收藏按钮及数量显示</li>
          <li>分享按钮</li>
          <li>评论数量显示</li>
        </ul>
      </li>
      <li><strong>标签系统：</strong>
        <ul>
          <li>红色背景的分类标签</li>
          <li>支持多个标签显示</li>
        </ul>
      </li>
    </ul>

    <h3>2. 右侧评论区域新增需求</h3>
    <ul>
      <li><strong>用户信息区域：</strong>
        <ul>
          <li>作者头像（圆形，较大尺寸）</li>
          <li>作者昵称和认证标识</li>
          <li>关注/已关注按钮</li>
          <li>发布时间</li>
        </ul>
      </li>
      <li><strong>内容描述：</strong>
        <ul>
          <li>笔记的详细文字描述</li>
          <li>支持@用户和话题标签的高亮显示</li>
          <li>展开/收起长文本功能</li>
        </ul>
      </li>
      <li><strong>评论系统：</strong>
        <ul>
          <li>评论列表展示</li>
          <li>评论输入框</li>
          <li>评论的点赞功能</li>
          <li>回复评论功能</li>
        </ul>
      </li>
      <li><strong>互动统计：</strong>
        <ul>
          <li>点赞数量</li>
          <li>收藏数量</li>
          <li>评论数量</li>
          <li>分享数量</li>
        </ul>
      </li>
    </ul>

  </div>
</body>
</html>