// @ts-nocheck
import { ApolloClient, InMemoryCache, gql } from '@apollo/client';
import { POST_TEMPLATE_FRAGMENT } from './graphql/fragments';
import { DEFAULT_ROUTE_PREFIXES } from '@/utils/route-config';
import { getServerApollo } from './server-apollo-client';
import { GET_CONTENT_NODE_COMMENTS } from './graphql/queries';
import { COMMENT_FRAGMENT } from './graphql/fragments';

// 创建Apollo客户端
export const apolloClient = new ApolloClient({
  uri: process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql',
  cache: new InMemoryCache(),
});

// 根据短UUID获取文章的GraphQL查询
const POST_BY_UUID_QUERY = gql`
  query GetPostByUuid($uuid: String!) {
    postByUuid(uuid: $uuid) {
      id
      databaseId
      title
      slug
      uri
      date
      excerpt
      content
      shortUuid
      likeCount: reactionCount(type: LIKE)
      userHasLiked: userHasReacted(type: LIKE)
      bookmarkCount: reactionCount(type: BOOKMARK)
      userHasBookmarked: userHasReacted(type: BOOKMARK)
      recommendCount: reactionCount(type: RECOMMEND)
      userHasRecommended: userHasReacted(type: RECOMMEND)
      commentStatus
      comments(first: 20) {
        nodes {
          id
          content
          date
          parentId
          author {
            node {
              name
              avatar {
                url
              }
            }
          }
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
      featuredImage {
        node {
          sourceUrl
          altText
        }
      }
      shareImage
      author {
        node {
          name
          slug
          avatar {
            url
          }
        }
      }
      categories {
        nodes {
          id
          name
          slug
        }
      }
      tags {
        nodes {
          id
          name
          slug
        }
      }
      relatedPosts {
        id
        title
        slug
        shortUuid
        date
        featuredImage {
          node {
            sourceUrl
            altText
          }
        }
        categories {
          nodes {
            id
            name
            slug
          }
        }
        likeCount: reactionCount(type: LIKE)
        userHasLiked: userHasReacted(type: LIKE)
        bookmarkCount: reactionCount(type: BOOKMARK)
        userHasBookmarked: userHasReacted(type: BOOKMARK)
        recommendCount: reactionCount(type: RECOMMEND)
        userHasRecommended: userHasReacted(type: RECOMMEND)
      }
      aiSeoTitle
      aiSeoDescription
      aiSeoJsonLd
      # 付费墙相关字段
      unlockPrice
      requiredMemberLevel
      isUnlockedByCurrentUser
      paywallInfo {
        hasPaywall
        previewContent
        loginUrl
        registerUrl
        upgradeUrl
        message
        isLoggedIn
      }
      postTemplate {
        ...PostTemplateFields
      }
    }
  }
  ${POST_TEMPLATE_FRAGMENT}
`;

const RESOLVE_SLUG_QUERY = gql`
  query ResolveSlug($slug: String!) {
    resolveSinglePathSlug(slug: $slug) {
      id
      type
      taxonomy
    }
  }
`;

const GET_PAGE_BY_DATABASE_ID_QUERY = gql`
  query GetPageByDatabaseId($id: ID!) {
    page(id: $id, idType: DATABASE_ID) {
      id
      databaseId
      title
      slug
      content
      featuredImage {
        node {
          sourceUrl
          altText
        }
      }
      blocks {
        __typename
        name
        isDynamic
        dynamicContent
        saveContent
        attributesJSON
        innerBlocks {
          __typename
          name
          isDynamic
          dynamicContent
          saveContent
          attributesJSON
        }
      }
      # AI SEO 字段
      aiSeoTitle
      aiSeoDescription
      aiSeoJsonLd
    }
  }
`;

/**
 * 验证UUID格式是否符合YYMMDD-123456格式
 * @param uuid 要验证的UUID
 * @returns 是否是有效的UUID
 */
export function isValidUuid(uuid: string): boolean {
  return Boolean(uuid && uuid.match(/^\d{6}-\d{6}$/));
}

/**
 * 根据短UUID获取文章
 * @param uuid 文章短UUID（格式：YYMMDD-123456）
 * @returns 文章数据
 */
export async function getPostByUuid(uuid: string) {
  // 验证UUID格式
  if (!isValidUuid(uuid)) {
    console.warn(`警告: UUID格式不正确 "${uuid}"，应为YYMMDD-123456格式`);
  }
  try {
    const client = typeof window === 'undefined' ? getServerApollo() : apolloClient;
    const { data } = await client.query({
      query: POST_BY_UUID_QUERY,
      variables: { uuid }
    });
    if (data?.postByUuid) {
      return data.postByUuid;
    }
    return null;
  } catch (error) {
    console.error('Error fetching post by UUID:', error);
    return null;
  }
}

/**
 * 获取所有文章
 * @param first 获取数量，默认10篇
 * @returns 文章列表
 */
export async function getAllPosts(first = 10) {
  const ALL_POSTS_QUERY = gql`
    query GetAllPosts($first: Int) {
      posts(first: $first) {
        nodes {
          id
          title
          slug
          shortUuid
          date
        }
      }
    }
  `;
  
  try {
    const { data } = await apolloClient.query({
      query: ALL_POSTS_QUERY,
      variables: { first }
    });
    
    return data?.posts?.nodes || [];
  } catch (error) {
    console.error('Error fetching all posts:', error);
    return [];
  }
}

// 获取站点设置
export async function getSiteSettings() {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: `
            query GetSiteSettings {
              generalSettings {
                title
                description
              }
            }
          `
        }),
        next: { revalidate: 3600 } // 1小时缓存
      }
    );
    
    const data = await response.json();
    return data?.data?.generalSettings || {};
  } catch (error) {
    console.error('Error fetching site settings:', error);
    return {};
  }
}

// 获取自定义类型内容列表
export async function getCustomPostsList(type: string, first = 10, after: string | null = null) {
  try {
    const contentType = type.toUpperCase();
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: `
            query GetCustomPosts(
              $type: [ContentTypeEnum]
              $first: Int
              $after: String
            ) {
              contentNodes(first: $first, after: $after, where: { contentTypes: $type }) {
                nodes {
                  __typename
                  id
                  databaseId
                  date
                  slug
                  uri
                  ... on NodeWithTitle {
                    title
                  }
                  ... on NodeWithExcerpt {
                    excerpt
                  }
                  ... on NodeWithFeaturedImage {
                    featuredImage {
                      node {
                        sourceUrl
                        altText
                      }
                    }
                  }
                  ... on NodeWithAuthor {
                    author {
                      node {
                        id
                        name
                        slug
                      }
                    }
                  }
                  ... on Note {
                    title
                    excerpt
                    content
                    shortUuid
                  }
                }
                pageInfo {
                  hasNextPage
                  endCursor
                }
              }
            }
          `,
          variables: {
            type: [contentType],
            first,
            after,
          }
        }),
        next: { revalidate: 300 } // 使用5分钟缓存
      }
    );
    
    const data = await response.json();
    return {
      posts: data?.data?.contentNodes?.nodes || [],
      pageInfo: data?.data?.contentNodes?.pageInfo || { hasNextPage: false, endCursor: null },
    };
  } catch (error) {
    console.error(`Error fetching ${type} list:`, error);
    return {
      posts: [],
      pageInfo: { hasNextPage: false, endCursor: null },
    };
  }
}

// 新增：获取路由前缀设置
export async function getRoutePrefixes() {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: `
            query GetRoutePrefixes {
              routePrefixes {
                postPrefix
                categoryPrefix
                tagPrefix
                categoryIndexRoute
                tagIndexRoute
                customTypePrefix
              }
            }
          `
        }),
        next: { revalidate: 3600 } // 1小时缓存
      }
    );
    const data = await response.json();
    // 将合并默认值的逻辑也统一到这里
    return data?.data?.routePrefixes
      ? { ...DEFAULT_ROUTE_PREFIXES, ...data.data.routePrefixes }
      : DEFAULT_ROUTE_PREFIXES;
  } catch (error) {
    console.error('Error fetching route prefixes:', error);
    return DEFAULT_ROUTE_PREFIXES; // 出错时返回默认值
  }
}

// 新增：获取自定义类型视图设置
export async function getCustomTypeViewSettings() {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: `
            query GetCustomTypeViewSettings {
              customTypeViewSettings {
                settings {
                  typeName
                  viewComponent
                }
              }
            }
          `
        }),
        next: { revalidate: 300 } // 5分钟缓存
      }
    );
    const data = await response.json();
    return data?.data?.customTypeViewSettings?.settings || [];
  } catch (error) {
    console.error('Error fetching view settings:', error);
    return [];
  }
}

// 获取单个自定义类型内容（通过UUID）
export async function getCustomPostByUuid(type: string, uuid: string) {
  if (!isValidUuid(uuid)) {
    console.warn(`警告: UUID格式不正确 "${uuid}"，应为YYMMDD-123456格式`);
  }
  const typeName = type.charAt(0).toUpperCase() + type.slice(1);

  const GET_CONTENT_NODE_BY_UUID_QUERY = gql`
            query GetContentNodeByUuid($uuid: String!) {
              contentNodeByUuid(uuid: $uuid) {
                __typename
                id
                databaseId
                slug
                uri
                date
                ... on NodeWithTitle {
                  title
                }
                ... on NodeWithExcerpt {
                  excerpt
                }
                ... on NodeWithContentEditor {
                  content
                }
                ... on NodeWithComments {
                  commentCount
                  commentStatus
                }
                ... on NodeWithFeaturedImage {
                  featuredImage {
                    node {
                      sourceUrl
                      altText
                    }
                  }
                }
                ... on NodeWithAuthor {
                  author {
                    node {
                      id
                      name
                      slug
                    }
                  }
                }
                shareImage
                featuredImageUrl
                readingTime
                aiSeoTitle
                aiSeoDescription
                aiSeoJsonLd
                ... on ${typeName} {
                    likeCount: reactionCount(type: LIKE)
                    userHasLiked: userHasReacted(type: LIKE)
                    bookmarkCount: reactionCount(type: BOOKMARK)
                    userHasBookmarked: userHasReacted(type: BOOKMARK)
                    recommendCount: reactionCount(type: RECOMMEND)
                    userHasRecommended: userHasReacted(type: RECOMMEND)
                    unlockPrice
                    requiredMemberLevel
                    isUnlockedByCurrentUser
                    isMembersOnly
                  }
              }
            }
  `;
  try {
    const client = typeof window === 'undefined' ? getServerApollo() : apolloClient;
    const { data } = await client.query({
      query: GET_CONTENT_NODE_BY_UUID_QUERY,
      variables: { uuid },
      fetchPolicy: 'no-cache', // 确保每次动态获取，解决用户状态缓存问题
    });
    
    const post = data?.contentNodeByUuid;
    if (post) {
      return post;
    }
    
    console.warn(`通过UUID ${uuid} 未找到内容`);
    return null;
  } catch (error) {
    console.error(`Error fetching content by UUID ${uuid}:`, error);
    return null;
  }
}

/**
 * NEW DYNAMIC FUNCTION
 * 获取任何内容节点的完整评论列表（扁平化）
 * @param databaseId 节点数据库ID
 * @param type CPT的类型名称, e.g., 'note'
 * @returns 评论数据 { nodes, pageInfo }
 */
export async function getCommentsForNode(databaseId: number, type: string) {
  if (!databaseId || !type) return null;

  // e.g. 'note' -> 'Note'
  const typeName = type.charAt(0).toUpperCase() + type.slice(1);

  const GET_DYNAMIC_COMMENTS = gql`
    query GetContentNodeComments($id: ID!, $first: Int = 100, $after: String) {
      contentNode(id: $id, idType: DATABASE_ID) {
        __typename
        ... on ${typeName} {
          comments(first: $first, after: $after) {
            nodes {
              ...CommentFields
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      }
    }
    ${COMMENT_FRAGMENT}
  `;

  try {
    const client = getServerApollo();
    const { data } = await client.query({
      query: GET_DYNAMIC_COMMENTS,
      variables: {
        id: databaseId,
        first: 100,
        after: null,
      },
    });

    console.log('--- [API DEBUG] getCommentsForNode ---');
    console.log('Variables sent:', JSON.stringify({ id: databaseId, type: typeName }));
    if (data.errors) {
      console.error('GraphQL Errors:', JSON.stringify(data.errors, null, 2));
    }
    console.log('Data received:', JSON.stringify(data, null, 2));
    console.log('--- [API DEBUG] End ---');

    // The result is nested under the `__typename` (e.g., data.contentNode.Note.comments)
    // The previous implementation was incorrect.
    const commentsData = data?.contentNode?.comments;

    return commentsData || null;

  } catch (error) {
    console.error(`Error fetching comments for node ${databaseId} of type ${type}:`, error);
    return null;
  }
}

/**
 * 获取 FD Member 插件的全局设置
 */
export async function getFdMemberSettings() {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql'}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: `
            query GetFdMemberSettings {
              fdMemberSettings {
                paywallVariant
              }
            }
          `
        }),
        next: { revalidate: 3600 } // 1小时缓存
      }
    );
    const data = await response.json();
    return data?.data?.fdMemberSettings || { paywallVariant: 'default' };
  } catch (error) {
    console.error('Error fetching fdMemberSettings:', error);
    return { paywallVariant: 'default' }; // 出错时返回默认值
  }
}

/**
 * 从WordPress REST API获取CPT信息（包含预构建的GraphQL Fragment）
 * @param type 自定义文章类型的slug
 * @returns 包含listFragment和cacheTag的对象
 */
export async function getCptInfo(type: string) {
  // 构建WordPress REST API URL
  const wpBaseUrl = process.env.NEXT_PUBLIC_WORDPRESS_URL ||
                   process.env.NEXT_PUBLIC_WORDPRESS_API_URL?.replace('/graphql', '') ||
                   'http://wordpress';

  try {
    const response = await fetch(`${wpBaseUrl}/wp-json/fd/v1/cpt-info/${type}`, {
      method: 'GET',
      next: { revalidate: 3600 }, // 缓存1小时，因为后端已有12小时缓存
    });

    if (!response.ok) {
      console.error(`Error fetching CPT info for ${type}: ${response.status} ${response.statusText}`);
      return { isValid: false };
    }

    const data = await response.json();
    console.log(`[CPT API] Successfully fetched CPT info for ${type}:`, data);
    return data;
  } catch (error) {
    console.error(`Error fetching CPT info for ${type}:`, error);
    return { isValid: false };
  }
}

/**
 * 使用预构建的查询获取CPT初始数据
 * @param type 自定义文章类型的slug
 * @param fullQuery 完整的GraphQL查询字符串
 * @param first 获取的文章数量
 * @returns 文章列表和分页信息
 */
export async function getCptInitialData(type: string, fullQuery: string, first = 12) {
  try {
    const response = await fetch(
      process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8080/graphql',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: fullQuery,
          variables: {
            first,
            after: null,
          }
        }),
        next: {
          revalidate: 300, // 5分钟缓存
          tags: [`cpt-list:${type}`] // 使用后端提供的缓存标签
        }
      }
    );

    const data = await response.json();
    console.log(`[CPT API] GraphQL response for ${type}:`, data);

    if (data.errors) {
      console.error(`GraphQL errors for ${type}:`, data.errors);
      return { nodes: [], pageInfo: { hasNextPage: false, endCursor: null } };
    }

    return data?.data?.contentNodes || { nodes: [], pageInfo: { hasNextPage: false, endCursor: null } };
  } catch (error) {
    console.error(`Error fetching initial data for ${type}:`, error);
    return { nodes: [], pageInfo: { hasNextPage: false, endCursor: null } };
  }
}

// 获取单个页面（通过slug）
export async function getPageBySlug(slug: string) {
  try {
    // 步骤1: 使用 fetch 解析 slug
    const slugResponse = await fetch(process.env.NEXT_PUBLIC_WORDPRESS_API_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query: RESOLVE_SLUG_QUERY.loc.source.body,
        variables: { slug },
      }),
      // 应用Next.js的请求缓存和去重机制
      next: { revalidate: 3600 },
      // 添加超时设置
      signal: AbortSignal.timeout(10000), // 10秒超时
    });

    const slugJson = await slugResponse.json();
    const resolved = slugJson.data?.resolveSinglePathSlug;

    if (!resolved || resolved.type !== 'page') {
      console.warn(`Slug "${slug}" could not be resolved or is not a page.`);
      return null;
    }

    // 步骤2: 使用 fetch 和数据库ID获取页面数据
    const pageId = resolved.id;
    const pageResponse = await fetch(process.env.NEXT_PUBLIC_WORDPRESS_API_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query: GET_PAGE_BY_DATABASE_ID_QUERY.loc.source.body,
        variables: { id: pageId },
      }),
      // 应用Next.js的请求缓存和去重机制
      next: { revalidate: 3600, tags: [`page:${slug}`] },
      // 添加超时设置
      signal: AbortSignal.timeout(10000), // 10秒超时
    });

    const pageJson = await pageResponse.json();

    if (pageJson.data?.page) {
      return pageJson.data.page;
    }

    return null;
  } catch (error) {
    console.error(`Error fetching page by slug "${slug}":`, error);
    return null;
  }
}