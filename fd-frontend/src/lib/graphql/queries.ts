import { gql } from '@apollo/client';
import {
  POST_FRAGMENT,
  POST_DETAIL_FRAGMENT,
  CATEGORY_FRAGMENT,
  TAG_FRAGMENT,
  USER_FRAGMENT,
  USER_DETAIL_FRAGMENT,
  MEDIA_FRAGMENT,
  MENU_ITEM_FRAGMENT,
  CUSTOM_POST_FRAGMENT,
  COMMENT_FRAGMENT,
  COMMENT_WITH_POST_FRAGMENT,
  NOTIFICATION_FRAGMENT
} from './fragments';

// 获取最新文章列表
export const GET_LATEST_POSTS = gql`
  query GetLatestPosts($first: Int = 10) {
    posts(first: $first, where: { orderby: { field: DATE, order: DESC } }) {
      nodes {
        ...PostFields
      }
    }
  }
  ${POST_FRAGMENT}
`;

// 获取单个文章详情
export const GET_POST_BY_SLUG = gql`
  query GetPostBySlug($slug: ID!) {
    post(id: $slug, idType: SLUG) {
      ...PostDetailFields
    }
  }
  ${POST_DETAIL_FRAGMENT}
`;

// 获取单个文章详情（通过ID）
export const GET_POST_BY_ID = gql`
  query GetPostById($id: ID!) {
    post(id: $id, idType: DATABASE_ID) {
      ...PostDetailFields
    }
  }
  ${POST_DETAIL_FRAGMENT}
`;

// 搜索文章
export const SEARCH_POSTS = gql`
  query SearchPosts($search: String!, $first: Int = 10) {
    posts(first: $first, where: { search: $search }) {
      nodes {
        ...PostFields
      }
    }
  }
  ${POST_FRAGMENT}
`;

// 获取所有分类
export const GET_CATEGORIES = gql`
  query GetCategories($first: Int = 1000) {
    categories(first: $first) {
      nodes {
        ...CategoryFields
      }
    }
  }
  ${CATEGORY_FRAGMENT}
`;

// 获取单个分类信息（通过slug）
export const GET_CATEGORY_BY_SLUG = gql`
  query GetCategoryBySlug($slug: ID!) {
    category(id: $slug, idType: SLUG) {
      ...CategoryFields
    }
  }
  ${CATEGORY_FRAGMENT}
`;

// 获取分类下的文章
export const GET_POSTS_BY_CATEGORY = gql`
  query GetPostsByCategory($categoryId: Int!, $first: Int = 10, $after: String) {
    posts(first: $first, after: $after, where: { categoryId: $categoryId }) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        ...PostFields
      }
    }
  }
  ${POST_FRAGMENT}
`;

// 获取所有标签
export const GET_TAGS = gql`
  query GetTags($first: Int = 1000) {
    tags(first: $first) {
      nodes {
        ...TagFields
      }
    }
  }
  ${TAG_FRAGMENT}
`;

// 获取标签下的文章
export const GET_POSTS_BY_TAG = gql`
  query GetPostsByTag($tagId: String!, $first: Int = 10, $after: String) {
    posts(first: $first, after: $after, where: { tagId: $tagId }) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        ...PostFields
      }
    }
  }
  ${POST_FRAGMENT}
`;

// 获取用户信息
export const GET_USER = gql`
  query GetUser($id: ID!) {
    user(id: $id, idType: DATABASE_ID) {
      ...UserFields
    }
  }
  ${USER_FRAGMENT}
`;

// 获取用户详细信息
export const GET_USER_DETAIL = gql`
  query GetUserDetail($id: ID!) {
    user(id: $id, idType: DATABASE_ID) {
      ...UserDetailFields
    }
  }
  ${USER_DETAIL_FRAGMENT}
`;

// 通过用户名获取用户
export const GET_USER_BY_USERNAME = gql`
  query GetUserByUsername($username: String!) {
    user(where: { login: $username }) {
      ...UserDetailFields
    }
  }
  ${USER_DETAIL_FRAGMENT}
`;

// 获取当前登录用户
export const GET_CURRENT_USER = gql`
  query GetCurrentUser {
    viewer {
      ...UserDetailFields
    }
  }
  ${USER_DETAIL_FRAGMENT}
`;

// 获取用户列表
export const GET_USERS = gql`
  query GetUsers($first: Int = 10) {
    users(first: $first) {
      nodes {
        ...UserFields
      }
    }
  }
  ${USER_FRAGMENT}
`;

// 搜索用户（用于私信搜索）
export const SEARCH_USERS = gql`
  query SearchUsers($search: String!) {
    searchableUsers(search: $search) {
      id
      databaseId
      name
      slug
      avatar {
        url
      }
    }
  }
`;

// 获取用户的文章
export const GET_POSTS_BY_AUTHOR = gql`
  query GetPostsByAuthor($authorId: Int!, $first: Int = 10, $after: String) {
    posts(first: $first, after: $after, where: { author: $authorId }) {
      pageInfo {
        endCursor
        hasNextPage
      }
      nodes {
        ...PostFields
        author {
          node {
            id
            name
            slug
            avatar {
              url
            }
          }
        }
      }
    }
  }
  ${POST_FRAGMENT}
`;

// 获取媒体详情
export const GET_MEDIA = gql`
  query GetMedia($id: ID!) {
    mediaItem(id: $id, idType: DATABASE_ID) {
      ...MediaFields
    }
  }
  ${MEDIA_FRAGMENT}
`;

// 获取所有菜单（不使用位置枚举）
export const GET_MENUS = gql`
  query GetMenus {
    menus {
      nodes {
        id
        name
        menuItems {
          nodes {
            ...MenuItemFields
          }
        }
      }
    }
  }
  ${MENU_ITEM_FRAGMENT}
`;

// 修改自定义内容类型列表查询，使用内联片段访问title字段
export const GET_CUSTOM_POSTS = gql`
  query GetCustomPosts($type: ContentTypeEnum!, $first: Int = 10) {
    contentNodes(first: $first, where: { contentTypes: [$type] }) {
      nodes {
        __typename
        id
        date
        slug
        uri
        ... on NodeWithTitle {
          title
        }
        ... on NodeWithFeaturedImage {
          featuredImage {
            node {
              sourceUrl
              altText
            }
          }
        }
        ... on NodeWithAuthor {
          author {
            node {
              id
              name
              slug
            }
          }
        }
      }
    }
  }
`;

// 获取自定义内容类型详情（通过slug）
export const GET_CUSTOM_POST_BY_SLUG = gql`
  query GetCustomPostBySlug($type: ContentTypeEnum!, $slug: String!) {
    contentNodes(
      first: 1,
      where: {
        contentTypes: [$type],
        name: $slug
      }
    ) {
      nodes {
        ...CustomPostFields
        __typename
        ... on NodeWithContentEditor {
          content
        }
        ... on NodeWithExcerpt {
          excerpt
        }
      }
    }
  }
  ${CUSTOM_POST_FRAGMENT}
`;

// 获取首页数据（可定制）
export const GET_HOME_DATA = gql`
  query GetHomeData($featuredPostsCount: Int = 5, $recentPostsCount: Int = 10) {
    featuredPosts: posts(first: $featuredPostsCount, where: { tag: "featured" }) {
      nodes {
        ...PostFields
      }
    }
    recentPosts: posts(first: $recentPostsCount, where: { orderby: { field: DATE, order: DESC } }) {
      nodes {
        ...PostFields
      }
    }
    categories(first: 10) {
      nodes {
        ...CategoryFields
      }
    }
  }
  ${POST_FRAGMENT}
  ${CATEGORY_FRAGMENT}
`;

// 获取所有页面
export const GET_PAGES = gql`
  query GetPages($first: Int = 10) {
    pages(first: $first) {
      nodes {
        id
        title
        date
        slug
        uri
        featuredImage {
          node {
            sourceUrl
            altText
          }
        }
        # AI SEO 字段
        aiSeoTitle
        aiSeoDescription
        aiSeoJsonLd
      }
    }
  }
`;

// 获取页面详情（通过slug）
export const GET_PAGE_BY_SLUG = gql`
  query GetPageBySlug($slug: ID!) {
    page(id: $slug, idType: URI) {
      id
      title
      content
      slug
      uri
      date
      author {
        node {
          name
          avatar {
            url
          }
        }
      }
      featuredImage {
        node {
          sourceUrl
          altText
        }
      }
      blocks {
        __typename
        name
        isDynamic
        dynamicContent
        saveContent
        attributesJSON
        innerBlocks {
          __typename
          name
          isDynamic
          dynamicContent
          saveContent
          attributesJSON
        }
      }
      # AI SEO 字段
      aiSeoTitle
      aiSeoDescription
      aiSeoJsonLd
    }
  }
`;

// 获取页面详情（通过ID）
export const GET_PAGE_BY_ID = gql`
  query GetPageById($id: ID!) {
    page(id: $id, idType: DATABASE_ID) {
      id
      title
      date
      slug
      uri
      content
      featuredImage {
        node {
          sourceUrl
          altText
        }
      }
      author {
        node {
          id
          name
          slug
          avatar {
            url
          }
        }
      }
      blocks {
        __typename
        name
        isDynamic
        dynamicContent
        saveContent
        attributesJSON
        innerBlocks {
          __typename
          name
          isDynamic
          dynamicContent
          saveContent
          attributesJSON
        }
      }
      # AI SEO 字段
      aiSeoTitle
      aiSeoDescription
      aiSeoJsonLd
    }
  }
`;

// 获取自定义内容类型详情（通过ID）
export const GET_CUSTOM_POST_BY_ID = gql`
  query GetCustomPostById($type: ContentTypeEnum!, $id: ID!) {
    contentNode(id: $id, idType: DATABASE_ID, contentType: $type) {
      ...CustomPostFields
      __typename
      ... on NodeWithContentEditor {
        content
      }
      ... on NodeWithExcerpt {
        excerpt
      }
    }
  }
  ${CUSTOM_POST_FRAGMENT}
`;

/**
 * 【重要】自定义分类法命名约定：
 * 1. 内置分类法直接使用名称，如 CATEGORY、TAG 等
 * 2. 自定义分类法也直接使用名称，如 COMPANY、PRODUCT 等
 * 3. 查询时必须使用正确的格式，例如：{ taxonomy: "COMPANY" }
 * 4. 可用的TaxonomyEnum值包括: CATEGORY, TAG 以及其他自定义分类法名称
 */

// 获取自定义分类法
export const GET_TAXONOMIES = gql`
  query GetTaxonomies {
    taxonomies {
      nodes {
        name
        description
        hierarchical
        label
        restBase
      }
    }
  }
`;

// 获取特定自定义分类法的所有项
export const GET_TAXONOMY_TERMS = gql`
  query GetTaxonomyTerms($taxonomy: TaxonomyEnum!) {
    terms(where: { taxonomies: [$taxonomy] }) {
      nodes {
        __typename
        id
        name
        slug
        uri
        ... on Category {
          count
          description
        }
        ... on Tag {
          count
        }
        ... on PostFormat {
          description
        }
      }
    }
  }
`;

// 通过ID获取分类法条目详情 - 简化版
export const GET_TAXONOMY_TERM_BY_ID = gql`
  query GetTaxonomyTermById($id: ID!, $taxonomy: TaxonomyEnum!) {
    terms(where: { taxonomies: [$taxonomy], include: [$id] }) {
      nodes {
        __typename
        id
        databaseId
        name
        slug
        uri
      }
    }
  }
`;

// 通过Slug获取分类法条目详情 - 简化版
export const GET_TAXONOMY_TERM_BY_SLUG = gql`
  query GetTaxonomyTermBySlug($slug: [String]!, $taxonomy: TaxonomyEnum!) {
    terms(where: { taxonomies: [$taxonomy], slug: $slug }) {
      nodes {
        __typename
        id
        databaseId
        name
        slug
        uri
      }
    }
  }
`;

// 获取分类法条目下的文章
export const GET_POSTS_BY_TAXONOMY = gql`
  query GetPostsByTaxonomy($taxonomy: TaxonomyEnum!, $termId: ID!, $first: Int = 10) {
    posts(
      first: $first
    ) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        ...PostFields
      }
    }
    terms(where: { taxonomies: [$taxonomy], include: [$termId] }) {
      nodes {
        __typename
        id
        name
        slug
        uri
        ... on Category {
          count
          description
        }
        ... on Tag {
          count
        }
      }
    }
  }
  ${POST_FRAGMENT}
`;

// 获取标签详情及其文章
export const GET_TAG_DETAIL = gql`
  query GetTagDetail($slug: ID!, $first: Int = 10) {
    tag(id: $slug, idType: SLUG) {
      ...TagFields
      description
      posts(first: $first) {
        nodes {
          ...PostFields
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  }
  ${TAG_FRAGMENT}
  ${POST_FRAGMENT}
`;

// 获取分类下的单篇文章详情
export const GET_CATEGORY_POST = gql`
  query GetCategoryPost($categoryId: Int!, $postId: ID!) {
    posts(where: { categoryId: $categoryId }) {
      nodes {
        ...PostDetailFields
        id
      }
    }
    post(id: $postId, idType: DATABASE_ID) {
      ...PostDetailFields
    }
  }
  ${POST_DETAIL_FRAGMENT}
`;

// 获取分类下的单篇文章详情(通过slug)
export const GET_CATEGORY_POST_BY_SLUG = gql`
  query GetCategoryPostBySlug($categoryId: Int!, $postSlug: ID!) {
    posts(where: { categoryId: $categoryId }) {
      nodes {
        id
        title
        slug
      }
    }
    post(id: $postSlug, idType: SLUG) {
      ...PostDetailFields
      categories {
        nodes {
          id
          name
          slug
        }
      }
    }
  }
  ${POST_DETAIL_FRAGMENT}
`;

// 获取标签下的单篇文章详情
export const GET_TAG_POST = gql`
  query GetTagPost($tagId: String!, $postId: ID!) {
    posts(where: { tagId: $tagId }) {
      nodes {
        id
        title
        slug
      }
    }
    post(id: $postId, idType: DATABASE_ID) {
      ...PostDetailFields
      tags {
        nodes {
          id
          name
          slug
        }
      }
    }
  }
  ${POST_DETAIL_FRAGMENT}
`;

// 获取标签下的单篇文章详情(通过slug)
export const GET_TAG_POST_BY_SLUG = gql`
  query GetTagPostBySlug($tagId: String!, $postSlug: ID!) {
    posts(where: { tagId: $tagId }) {
      nodes {
        id
        title
        slug
      }
    }
    post(id: $postSlug, idType: SLUG) {
      ...PostDetailFields
      tags {
        nodes {
          id
          name
          slug
        }
      }
    }
  }
  ${POST_DETAIL_FRAGMENT}
`;

// 获取分类详情及其文章
export const GET_CATEGORY_DETAIL = gql`
  query GetCategoryDetail($slug: ID!, $first: Int = 10) {
    category(id: $slug, idType: SLUG) {
      ...CategoryFields
      description
      posts(first: $first) {
        nodes {
          ...PostFields
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  }
  ${CATEGORY_FRAGMENT}
  ${POST_FRAGMENT}
`;

// 获取自定义分类法详情
export const GET_TAXONOMY = gql`
  query GetTaxonomy($name: ID!) {
    taxonomy(id: $name, idType: NAME) {
      id
      name
      label
      description
      connectedContentTypes {
        nodes {
          name
          label
        }
      }
      hierarchical
      restBase
    }
  }
`;

// 获取自定义分类法术语详情
export const GET_TAXONOMY_TERM = gql`
  query GetTaxonomyTerm($taxonomy: TaxonomyEnum!, $slug: [String]!) {
    terms(where: { taxonomies: [$taxonomy], slug: $slug }) {
      nodes {
        __typename
        id
        databaseId
        name
        slug
        description
        uri
        # AI SEO 字段
        aiSeoTitle
        aiSeoDescription
        aiSeoJsonLd
        ... on Category {
          count
          children {
            nodes {
              id
              name
              slug
              uri
            }
          }
        }
        ... on Tag {
          count
        }
      }
    }
  }
`;

// 获取自定义分类法术语详情及其文章
export const GET_TAXONOMY_TERM_DETAIL = gql`
  query GetTaxonomyTermDetail($taxonomy: TaxonomyEnum!, $slug: [String]!, $first: Int = 10) {
    terms(where: { taxonomies: [$taxonomy], slug: $slug }) {
      nodes {
        __typename
        id
        databaseId
        name
        slug
        description
        uri
        # AI SEO 字段
        aiSeoTitle
        aiSeoDescription
        aiSeoJsonLd
        ... on Category {
          count
          posts(first: $first) {
            nodes {
              ...PostFields
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
          children {
            nodes {
              id
              name
              slug
              uri
            }
          }
        }
        ... on Tag {
          count
          posts(first: $first) {
            nodes {
              ...PostFields
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      }
    }
  }
  ${POST_FRAGMENT}
`;

// 获取标签详情（通过slug）
export const GET_TAG_BY_SLUG = gql`
  query GetTagBySlug($slug: ID!) {
    tag(id: $slug, idType: SLUG) {
      id
      name
      slug
      count
      description
      posts {
        nodes {
          id
          title
          date
          slug
        }
      }
    }
  }
`;

// 获取前端显示设置中启用的自定义分类法
export const GET_ENABLED_TAXONOMIES = gql`
  query GetEnabledTaxonomies {
    frontendDisplaySettings {
      taxonomies
    }
    taxonomies {
      nodes {
        name
        description
        hierarchical
        label
        restBase
      }
    }
  }
`;

// 获取前端显示设置中启用的自定义文章类型
export const GET_ENABLED_POST_TYPES = gql`
  query GetEnabledPostTypes {
    frontendDisplaySettings {
      postTypes
    }
    contentTypes {
      nodes {
        name
        label
        description
        hierarchical
        hasArchive
        labels {
          singularName
          name
          allItems
        }
      }
    }
  }
`;

// 使用taxQuery通过分类法ID获取文章
export const GET_POSTS_BY_TAX_QUERY_ID = gql`
  query GetPostsByTaxQueryId($taxonomy: TaxonomyEnum!, $termId: [String!], $first: Int = 10) {
    posts(
      first: $first,
      where: {
        taxQuery: {
          relation: AND,
          taxArray: [
            {
              taxonomy: $taxonomy,
              operator: IN,
              terms: $termId,
              field: ID
            }
          ]
        }
      }
    ) {
      nodes {
        ...PostFields
      }
    }
  }
  ${POST_FRAGMENT}
`;

// 使用taxQuery通过分类法Slug获取文章
export const GET_POSTS_BY_TAX_QUERY_SLUG = gql`
  query GetPostsByTaxQuerySlug($taxonomy: TaxonomyEnum!, $slugs: [String!], $first: Int = 10, $after: String) {
    posts(
      first: $first,
      after: $after,
      where: {
        taxQuery: {
          relation: AND,
          taxArray: [
            {
              taxonomy: $taxonomy,
              operator: IN,
              terms: $slugs,
              field: SLUG
            }
          ]
        }
      }
    ) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        ...PostFields
      }
    }
  }
  ${POST_FRAGMENT}
`;

// 使用taxQuery通过分类法ID获取文章详情
export const GET_POSTS_DETAIL_BY_TAX_QUERY_ID = gql`
  query GetPostsDetailByTaxQueryId($taxonomy: TaxonomyEnum!, $termId: [String!], $first: Int = 10) {
    posts(
      first: $first,
      where: {
        taxQuery: {
          relation: AND,
          taxArray: [
            {
              taxonomy: $taxonomy,
              operator: IN,
              terms: $termId,
              field: ID
            }
          ]
        }
      }
    ) {
      nodes {
        ...PostDetailFields
      }
    }
  }
  ${POST_DETAIL_FRAGMENT}
`;

// 使用taxQuery通过分类法Slug获取文章详情
export const GET_POSTS_DETAIL_BY_TAX_QUERY_SLUG = gql`
  query GetPostsDetailByTaxQuerySlug($taxonomy: TaxonomyEnum!, $slugs: [String!], $first: Int = 10) {
    posts(
      first: $first,
      where: {
        taxQuery: {
          relation: AND,
          taxArray: [
            {
              taxonomy: $taxonomy,
              operator: IN,
              terms: $slugs,
              field: SLUG
            }
          ]
        }
      }
    ) {
      nodes {
        ...PostDetailFields
      }
    }
  }
  ${POST_DETAIL_FRAGMENT}
`;

// 获取所有WordPress设置
export const GET_ALL_SETTINGS = gql`
  query GetAllSettings {
    allSettings {
      generalSettingsDateFormat
      generalSettingsDescription
      generalSettingsLanguage
      generalSettingsStartOfWeek
      generalSettingsTimeFormat
      generalSettingsTimezone
      generalSettingsTitle
      generalSettingsUrl
      readingSettingsPostsPerPage
      discussionSettingsDefaultCommentStatus
      discussionSettingsDefaultPingStatus
      writingSettingsDefaultCategory
      writingSettingsDefaultPostFormat
      writingSettingsUseSmilies
    }
  }
`;

// 获取常规设置
export const GET_GENERAL_SETTINGS = gql`
  query GetGeneralSettings {
    generalSettings {
      dateFormat
      description
      language
      startOfWeek
      timeFormat
      timezone
      title
      url
    }
  }
`;

// 获取阅读设置
export const GET_READING_SETTINGS = gql`
  query GetReadingSettings {
    readingSettings {
      postsPerPage
    }
  }
`;

// 获取讨论设置
export const GET_DISCUSSION_SETTINGS = gql`
  query GetDiscussionSettings {
    discussionSettings {
      defaultCommentStatus
      defaultPingStatus
      commentModeration
    }
  }
`;

// 获取写作设置
export const GET_WRITING_SETTINGS = gql`
  query GetWritingSettings {
    writingSettings {
      defaultCategory
      defaultPostFormat
      useSmilies
    }
  }
`;

// 获取文章的评论
export const GET_POST_COMMENTS = gql`
  query GetPostComments(
    $postId: ID!, 
    $first: Int = 100, 
    $after: String, 
    $order: OrderEnum = ASC, 
    $orderby: CommentsConnectionOrderbyEnum = COMMENT_DATE,
    $offset: Int = 0
  ) {
    post(id: $postId, idType: DATABASE_ID) {
      id
      title
      commentCount
      commentStatus
      comments(first: $first, after: $after, where: { order: $order, orderby: $orderby, offset: $offset }) {
        nodes {
          ...CommentFields
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  }
  ${COMMENT_FRAGMENT}
`;

// 获取单个评论详情
export const GET_COMMENT = gql`
  query GetComment($id: ID!) {
    comment(id: $id) {
      ...CommentFields
      post {
        id
        title
        slug
      }
    }
  }
  ${COMMENT_FRAGMENT}
`;

// 获取评论的回复
export const GET_COMMENT_REPLIES = gql`
  query GetCommentReplies($id: ID!, $first: Int = 50) {
    comment(id: $id) {
      ...CommentFields
      replies(first: $first) {
        nodes {
          ...CommentFields
        }
      }
    }
  }
  ${COMMENT_FRAGMENT}
`;

// 按状态过滤获取评论
export const GET_COMMENTS_BY_STATUS = gql`
  query GetCommentsByStatus($status: [CommentStatusEnum], $first: Int = 50) {
    comments(where: { statusIn: $status }, first: $first) {
      nodes {
        ...CommentWithPostFields
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
  ${COMMENT_WITH_POST_FRAGMENT}
`;

// 搜索内容（多类型）
export const SEARCH_CONTENT = gql`
  query SearchContent($search: String!, $types: [ContentTypeEnum], $first: Int = 10) {
    contentNodes(
      first: $first, 
      where: { 
        search: $search,
        contentTypes: $types
      }
    ) {
      nodes {
        __typename
        id
        databaseId
        ... on NodeWithTitle {
          title
        }
        ... on NodeWithContentEditor {
          content
        }
        ... on NodeWithExcerpt {
          excerpt
        }
        ... on NodeWithFeaturedImage {
          featuredImage {
            node {
              sourceUrl
              altText
            }
          }
        }
        ... on Post {
          shortUuid
          categories {
            nodes {
              name
              slug
            }
          }
        }
        ... on UniformResourceIdentifiable {
          uri
        }
        ... on ContentNode {
          slug
          date
          modified
        }
      }
    }
  }
`;

// 获取VI设置的GraphQL查询
export const GET_VI_SETTINGS = gql`
  query GetVISettings {
    viSettings {
      # 基础设置
      logoUrl
      logoDarkUrl
      faviconUrl
      
      # 颜色设置
      primaryColor
      secondaryColor
      backgroundColor
      darkModeEnabled
      darkBackgroundColor
      amberColor
      roseColor
      successColor
      errorColor
      
      # 排版设置
      headingFont
      bodyFont
      baseFontSize
      lineHeight
      spacingUnit
      
      # UI设计令牌
      radiusSmall
      radiusMedium
      radiusLarge
      shadowSmall
      shadowMedium
      shadowLarge
      
      # 搜索设置
      searchEngineType
      meilisearchApiUrl
      meilisearchApiKey
      meilisearchIndexName
    }
  }
`;

// 获取路由前缀设置
export const GET_ROUTE_PREFIXES = gql`
  query GetRoutePrefixes {
    routePrefixes {
      categoryPrefix
      tagPrefix
      postPrefix
      categoryIndexRoute
      tagIndexRoute
    }
  }
`;

// 获取标签索引页面数据
export const GET_TAG_INDEX_PAGE_DATA = gql`
  query GetTagIndexPageData {
    tagIndexPageData {
      seoSettings {
        aiSeoTitle
        aiSeoDescription
        aiSeoJsonLd
        enabled
        lastUpdated
      }
      tags {
        id
        databaseId
        name
        slug
        count
        bannerImageUrl
        bannerImage {
          sourceUrl
          altText
          mediaDetails {
            width
            height
          }
        }
      }
      statistics {
        totalTags
        totalPosts
        averagePostsPerTag
      }
    }
  }
`;

// 获取Slug映射表
export const GET_SLUG_MAPPING_TABLE = gql`
  query GetSlugMappingTable {
    slugMappingTable {
      slug
      type
      id
      taxonomy
    }
  }
`;

// 通过UUID获取文章
export const POST_BY_UUID_QUERY = gql`
  query GetPostByUuid($uuid: String!) {
    postByUuid(uuid: $uuid) {
      id
      databaseId
      title
      slug
      shortUuid
      date
    }
  }
`;

// 通过Meta获取文章（UUID备用查询）
export const POST_BY_UUID_FALLBACK_QUERY = gql`
  query GetPostByUuidFallback($uuid: String!) {
    posts(where: {metaQuery: {metaArray: [{key: "_fd_short_uuid", value: $uuid, compareOperator: EQUAL_TO}]}}, first: 1) {
      nodes {
        id
        databaseId
        title
        slug
        shortUuid
        date
      }
    }
  }
`;

// 根据内容节点ID获取评论（适用于任何内容类型）
export const GET_CONTENT_NODE_COMMENTS = gql`
  query GetContentNodeComments(
    $id: ID!, 
    $first: Int = 10, 
    $after: String,
    $order: OrderEnum = ASC,
    $orderby: CommentsConnectionOrderbyEnum = COMMENT_DATE,
    $offset: Int = 0
  ) {
    contentNode(id: $id, idType: DATABASE_ID) {
      __typename
      ... on Post {
        comments(first: $first, after: $after, where: { order: $order, orderby: $orderby, offset: $offset }) {
          nodes {
            ...CommentFields
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
      ... on Page {
        comments(first: $first, after: $after, where: { order: $order, orderby: $orderby, offset: $offset }) {
          nodes {
            ...CommentFields
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
      ... on MediaItem {
        comments(first: $first, after: $after, where: { order: $order, orderby: $orderby, offset: $offset }) {
          nodes {
            ...CommentFields
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
      ... on Note {
        comments(first: $first, after: $after, where: { order: $order, orderby: $orderby, offset: $offset }) {
          nodes {
            ...CommentFields
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
    }
  }
  ${COMMENT_FRAGMENT}
`;

// 检查账号是否已存在
export const CHECK_ACCOUNT_EXISTS = gql`
  query CheckAccountExists($type: String!, $value: String!) {
    checkAccountExists(type: $type, value: $value)
  }
`; 

// 获取支付方式列表
export const GET_PAYMENT_METHODS = gql`
  query GetPaymentMethods {
    paymentMethods {
      id
      title
      icon
      color
    }
  }
`;

// 查询单个订单信息
export const GET_PAYMENT_ORDER = gql`
  query GetPaymentOrder($id: ID!) {
    paymentOrder(id: $id) {
      id
      orderNumber
      title
      description
      amount
      userId
      paymentMethod
      paymentStatus
      createdAt
      updatedAt
    }
  }
`;

// 获取用户订单列表
export const GET_USER_ORDERS = gql`
  query GetUserOrders($first: Int, $after: String, $status: OrderStatusEnum) {
    viewer {
      id
      orders(first: $first, after: $after, status: $status) {
        nodes {
          id
          orderNumber
          title
          amount
          paymentMethod
          paymentStatus
          createdAt
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  }
`;

// 获取当前用户的余额和积分
export const GET_USER_BALANCE_AND_POINTS = gql`
  query GetUserBalanceAndPoints {
    viewer {
      id
      balance
      points
    }
  }
`;

// 获取用户通知列表
export const GET_USER_NOTIFICATIONS = gql`
  query GetUserNotifications($status: String, $type: String, $perPage: Int = 10, $page: Int = 1) {
    userNotifications(status: $status, type: $type, perPage: $perPage, page: $page) {
      ...NotificationFields
    }
  }
  ${NOTIFICATION_FRAGMENT}
`;

// 获取单个通知详情
export const GET_USER_NOTIFICATION = gql`
  query GetUserNotification($id: ID!) {
    userNotification(id: $id) {
      ...NotificationFields
    }
  }
  ${NOTIFICATION_FRAGMENT}
`;

// 获取未读通知数量
export const GET_UNREAD_NOTIFICATION_COUNT = gql`
  query GetUnreadNotificationCount {
    unreadNotificationCount
  }
`;

/**
 * Private Messaging Queries
 */

export const GET_MY_CONVERSATIONS = gql`
  query GetMyConversations($first: Int = 10, $after: String) {
    viewer {
      id
      privateConversations(first: $first, after: $after) {
        edges {
          cursor
          node {
            id
            databaseId
            updatedAt
            unreadCount
            otherUser {
              id
              name
              avatar {
                url
              }
            }
            lastMessage {
              content
              sentAt
            }
          }
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  }
`

export const GET_CONVERSATION_MESSAGES = gql`
  query GetConversationMessages($id: ID!, $first: Int = 20, $after: String) {
    privateConversation(id: $id) {
      id
      databaseId
      otherUser {
        id
        name
      }
      messages(first: $first, after: $after) {
        edges {
          node {
            id
            content
            sentAt
            isRead
            sender {
              id
              name
            }
          }
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  }
`

export const GET_UNREAD_MESSAGE_COUNT = gql`
  query GetUnreadMessageCount {
    viewer {
      id
      unreadMessageCount
    }
  }
`

// 获取用户交互过的文章列表
export const GET_USER_REACTIONS = gql`
  query GetUserReactions($type: ReactionTypeEnum!, $first: Int!, $after: String) {
    userReactions(type: $type, first: $first, after: $after) {
      nodes {
        id
        title
        slug
        shortUuid
        date
        contentType {
          node {
            name
          }
        }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
`;

// 获取前端列表分页设置
export const GET_POSTS_PER_PAGE_SETTING = gql`
  query GetPostsPerPageSetting {
    postsPerPageSetting
  }
`;