import { gql } from '@apollo/client';

// 文章基本信息片段
export const POST_FRAGMENT = gql`
  fragment PostFields on Post {
    id
    title
    date
    slug
    shortUuid
    excerpt
    featuredImage {
      node {
        sourceUrl
        altText
      }
    }
    categories {
      nodes {
        id
        name
        slug
      }
    }
    # AI SEO 字段
    aiSeoTitle
    aiSeoDescription
    aiSeoJsonLd
  }
`;

// 文章详细信息片段
export const POST_DETAIL_FRAGMENT = gql`
  fragment PostDetailFields on Post {
    id
    title
    date
    slug
    shortUuid
    excerpt
    content
    likesCount
    userHasLiked
    bookmarksCount
    userHasBookmarked
    recommendsCount
    userHasRecommended
    commentStatus
    comments(first: 100) {
      nodes {
        id
        databaseId
        content
        date
        parentId
        status
        author {
          node {
            name
            url
            avatar {
              url
            }
          }
        }
      }
    }
    featuredImage {
      node {
        sourceUrl
        altText
      }
    }
    categories {
      nodes {
        id
        name
        slug
      }
    }
    tags {
      nodes {
        id
        name
        slug
      }
    }
    author {
      node {
        id
        name
        slug
        avatar {
          url
        }
      }
    }
    # 付费墙相关字段
    unlockPrice
    requiredMemberLevel
    isUnlockedByCurrentUser
    paywallInfo {
      hasPaywall
      previewContent
      loginUrl
      registerUrl
      upgradeUrl
      message
      isLoggedIn
    }
    # AI SEO 字段
    aiSeoTitle
    aiSeoDescription
    aiSeoJsonLd
  }
`;

// 文章模板片段
export const POST_TEMPLATE_FRAGMENT = gql`
  fragment PostTemplateFields on PostTemplate {
    templateType
    subtitle
    articleAuthor
    articleSource
    copyrightType
    videoUrl
    additionalImages {
      nodes {
        sourceUrl
        altText
      }
    }
  }
`;

// 分类信息片段
export const CATEGORY_FRAGMENT = gql`
  fragment CategoryFields on Category {
    id
    databaseId
    name
    slug
    count
    description
    bannerImageUrl
    bannerImage {
      sourceUrl
      altText
      mediaDetails {
        width
        height
      }
    }
  }
`;

// 标签信息片段
export const TAG_FRAGMENT = gql`
  fragment TagFields on Tag {
    id
    databaseId
    name
    slug
    count
    description
    # AI SEO 字段
    aiSeoTitle
    aiSeoDescription
    aiSeoJsonLd
    # Banner 图片字段
    bannerImageUrl
    bannerImage {
      sourceUrl
      altText
      mediaDetails {
        width
        height
      }
    }
  }
`;

// 用户信息片段
export const USER_FRAGMENT = gql`
  fragment UserFields on User {
    id
    name
    slug
    description
    avatar {
      url
    }
  }
`;

// 用户详细信息片段
export const USER_DETAIL_FRAGMENT = gql`
  fragment UserDetailFields on User {
    id
    databaseId
    name
    firstName
    lastName
    email
    description
    nickname
    slug
    phone
    role
    roles {
      nodes {
        name
      }
    }
    avatar {
      url
    }
    memberLevel {
      id
      name
      description
      priority
      tier
    }
    memberExpiration
  }
`;

// 媒体文件信息片段
export const MEDIA_FRAGMENT = gql`
  fragment MediaFields on MediaItem {
    id
    title
    altText
    sourceUrl
    mediaItemUrl
    mediaType
    mimeType
  }
`;

// 菜单项信息片段
export const MENU_ITEM_FRAGMENT = gql`
  fragment MenuItemFields on MenuItem {
    id
    title
    label
    url
    target
    parentId
    cssClasses
  }
`;

// 自定义文章类型信息片段
export const CUSTOM_POST_FRAGMENT = gql`
  fragment CustomPostFields on ContentNode {
    id
    databaseId
    likesCount
    userHasLiked
    recommendsCount
    userHasRecommended
    bookmarksCount
    userHasBookmarked
    title
    date
    slug
    uri
    ... on NodeWithFeaturedImage {
      featuredImage {
        node {
          sourceUrl
          altText
        }
      }
    }
    ... on NodeWithAuthor {
      author {
        node {
          id
          name
          slug
        }
      }
    }
    # AI SEO 字段
    aiSeoTitle
    aiSeoDescription
    aiSeoJsonLd
  }
`;

// 评论信息片段
export const COMMENT_FRAGMENT = gql`
  fragment CommentFields on Comment {
    id
    databaseId
    content
    date
    parentId
    status
    author {
      node {
        name
        url
        avatar {
          url
        }
      }
    }
  }
`;

// 包含文章信息的评论片段
export const COMMENT_WITH_POST_FRAGMENT = gql`
  fragment CommentWithPostFields on Comment {
    ...CommentFields
    commentedOn {
      node {
        ... on Post {
          id
          title
          slug
        }
        ... on Page {
          id
          title
          slug
        }
      }
    }
  }
  ${COMMENT_FRAGMENT}
`;

// 通知信息片段
export const NOTIFICATION_FRAGMENT = gql`
  fragment NotificationFields on UserNotification {
    id
    userId
    title
    content
    type
    typeName
    status
    createdAt
    readAt
  }
`;

// 点赞、收藏、推荐字段片段
export const REACTION_FIELDS_FRAGMENT = gql`
  fragment ReactionFields on ContentNode {
    likeCount: reactionCount(type: LIKE)
    userHasLiked: userHasReacted(type: LIKE)
    bookmarkCount: reactionCount(type: BOOKMARK)
    userHasBookmarked: userHasReacted(type: BOOKMARK)
    recommendCount: reactionCount(type: RECOMMEND)
    userHasRecommended: userHasReacted(type: RECOMMEND)
  }
`; 