'use client';

import React, { useEffect, Suspense } from 'react';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import { useQuery, gql } from '@apollo/client';
import { Post as PostType } from '@/types/post';
import PostContentSmart from '@/components/post/PostContentSmart';
import CommentSection from '@/components/comments/CommentSection';
import NoteImageDisplay from '@/components/note/NoteImageDisplay';
import ReactionButton from '@/components/post/ReactionButton';
import { ReactionType } from '@/types/Reaction';
import { HiX, HiOutlineHeart, HiOutlineBookmark, HiOutlineShare } from 'react-icons/hi';
import Image from 'next/image';

// 为模态框专门创建的GraphQL查询
const GET_NOTE_DETAILS = gql`
  query GetNoteDetails($uuid: String!) {
    contentNodeByUuid(uuid: $uuid) {
      __typename
      ... on Note {
        id
        databaseId
        title
        content
        date
        commentStatus
        likeCount
        bookmarkCount
        userHasLiked
        userHasBookmarked
        featuredImage {
          node {
            sourceUrl
            altText
          }
        }
        author {
          node {
            name
            nickname
            avatar {
              url
            }
          }
        }
        comments {
          nodes {
            id
            content
            date
            parentId
            author {
              node {
                name
                avatar {
                  url
                }
              }
            }
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
    }
  }
`;

function ModalContent() {
  const router = useRouter();
  const params = useParams();
  
  const uuid = typeof params.uuid === 'string' ? params.uuid : '';

  const { data, loading, error } = useQuery(GET_NOTE_DETAILS, {
    variables: { uuid },
    skip: !uuid, // 如果没有uuid，则跳过查询
  });

  const post: PostType | null = data?.contentNodeByUuid;
  const commentsData = post?.comments;

  useEffect(() => {
    // 按下 'Esc' 键关闭模态框
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        router.back();
      }
    };
    window.addEventListener('keydown', handleEsc);
    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, [router]);

  const handleClose = () => {
    router.back();
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 animate-fade-in"
      onClick={handleClose}
    >
      <div
        className="bg-white rounded-lg shadow-2xl w-full max-w-7xl h-[90vh] flex overflow-hidden relative"
        onClick={(e: React.MouseEvent) => e.stopPropagation()} // 防止点击内容区域关闭模态框
      >
        <button
          onClick={handleClose}
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-800 z-10 p-2 rounded-full bg-white/50 hover:bg-white/80 transition-colors"
          aria-label="Close modal"
        >
          <HiX className="w-6 h-6" />
        </button>

        {loading ? (
          <div className="w-full flex items-center justify-center">
            <p className="text-gray-500">正在加载笔记...</p>
          </div>
        ) : error || !post ? (
          <div className="w-full flex items-center justify-center">
            <p className="text-red-500">无法加载笔记内容。错误: {error?.message}</p>
          </div>
        ) : (
          <>
            {/* 左侧：图片 */}
            <div className="w-1/2 bg-gray-100 relative hidden md:block">
              {post.featuredImage?.node?.sourceUrl ? (
                <Image
                  src={post.featuredImage.node.sourceUrl}
                  alt={post.featuredImage.node.altText || post.title}
                  layout="fill"
                  objectFit="cover"
                />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-400">
                  <span>无特色图片</span>
                </div>
              )}
            </div>

            {/* 右侧：内容和评论 */}
            <div className="w-full md:w-1/2 flex flex-col overflow-y-auto">
              <div className="p-6 flex-grow">
                <h1 className="text-2xl font-bold mb-4">{post.title}</h1>
                <div className="prose max-w-none">
                  <PostContentSmart
                    postId={post.databaseId || 0}
                    initialContent={post.content || ''}
                    postTitle={post.title}
                    isUnlocked={true} // 假设在模态框中总是解锁状态
                    graphqlSingleName={post.__typename}
                  />
                </div>
              </div>
              
              <div className="p-6 border-t border-gray-200 bg-gray-50">
                <CommentSection
                  postId={post.databaseId || 0}
                  commentStatus={post.commentStatus || 'closed'}
                  isCustomType={true}
                  initialData={{
                    nodes: commentsData?.nodes ?? [],
                    pageInfo: commentsData?.pageInfo ?? { hasNextPage: false, endCursor: null },
                  }}
                />
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

const NoteModalPage = () => {
  const searchParams = useSearchParams();
  const isModal = searchParams.get('modal') === 'true';

  // 使用 Suspense 是因为 useSearchParams() 可能会在初始渲染时挂起
  // 这是一个好习惯，可以防止潜在的渲染问题
  return (
    <Suspense fallback={null}>
      {isModal ? <ModalContent /> : null}
    </Suspense>
  );
};

export default NoteModalPage; 