'use client';

import React, { useEffect, Suspense } from 'react';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import { useQuery, gql } from '@apollo/client';
import { Post as PostType } from '@/types/post';
import PostContentSmart from '@/components/post/PostContentSmart';
import CommentSection from '@/components/comments/CommentSection';
import NoteImageDisplay from '@/components/note/NoteImageDisplay';
import ReactionButton from '@/components/post/ReactionButton';
import { ReactionType } from '@/types/Reaction';
import { HiX, HiOutlineHeart, HiOutlineBookmark, HiOutlineShare } from 'react-icons/hi';
import Image from 'next/image';

// 为模态框专门创建的GraphQL查询
const GET_NOTE_DETAILS = gql`
  query GetNoteDetails($uuid: String!) {
    contentNodeByUuid(uuid: $uuid) {
      __typename
      ... on Note {
        id
        databaseId
        title
        content
        date
        commentStatus
        likeCount
        bookmarkCount
        userHasLiked
        userHasBookmarked
        featuredImage {
          node {
            sourceUrl
            altText
          }
        }
        author {
          node {
            name
            nickname
            avatar {
              url
            }
          }
        }
        comments {
          nodes {
            id
            content
            date
            parentId
            author {
              node {
                name
                avatar {
                  url
                }
              }
            }
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
    }
  }
`;

function ModalContent() {
  const router = useRouter();
  const params = useParams();
  
  const uuid = typeof params.uuid === 'string' ? params.uuid : '';

  const { data, loading, error } = useQuery(GET_NOTE_DETAILS, {
    variables: { uuid },
    skip: !uuid, // 如果没有uuid，则跳过查询
  });

  const post: PostType | null = data?.contentNodeByUuid;
  const commentsData = post?.comments;

  useEffect(() => {
    // 按下 'Esc' 键关闭模态框
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        router.back();
      }
    };
    window.addEventListener('keydown', handleEsc);
    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, [router]);

  const handleClose = () => {
    router.back();
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 animate-fade-in"
      onClick={handleClose}
    >
      <div
        className="bg-white rounded-lg shadow-2xl w-full max-w-7xl h-[90vh] flex overflow-hidden relative"
        onClick={(e: React.MouseEvent) => e.stopPropagation()} // 防止点击内容区域关闭模态框
      >
        <button
          onClick={handleClose}
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-800 z-10 p-2 rounded-full bg-white/50 hover:bg-white/80 transition-colors"
          aria-label="Close modal"
        >
          <HiX className="w-6 h-6" />
        </button>

        {loading ? (
          <div className="w-full flex items-center justify-center">
            <p className="text-gray-500">正在加载笔记...</p>
          </div>
        ) : error || !post ? (
          <div className="w-full flex items-center justify-center">
            <p className="text-red-500">无法加载笔记内容。错误: {error?.message}</p>
          </div>
        ) : (
          <>
            {/* 左侧：图片轮播区域 (65%) */}
            <div className="w-full md:w-[65%] bg-black relative">
              <NoteImageDisplay
                featuredImage={post.featuredImage?.node}
                content={post.content || ''}
                className="w-full h-full"
                showCounter={true}
              />

              {/* 底部浮动操作栏 */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                <div className="flex items-center justify-between text-white">
                  <div className="flex items-center space-x-4">
                    <ReactionButton
                      postId={post.id}
                      initialCount={post.likeCount || 0}
                      initialState={post.userHasLiked || false}
                      type={ReactionType.Like}
                      icon={<HiOutlineHeart className="w-6 h-6" />}
                      activeClass="text-red-500"
                      inactiveClass="text-white hover:text-red-300"
                      successMessage="感谢点赞！"
                      undoMessage="已取消点赞"
                      variant="minimal"
                    />
                    <ReactionButton
                      postId={post.id}
                      initialCount={post.bookmarkCount || 0}
                      initialState={post.userHasBookmarked || false}
                      type={ReactionType.Bookmark}
                      icon={<HiOutlineBookmark className="w-6 h-6" />}
                      activeClass="text-yellow-500"
                      inactiveClass="text-white hover:text-yellow-300"
                      successMessage="已收藏"
                      undoMessage="已取消收藏"
                      variant="minimal"
                    />
                    <button className="flex items-center space-x-1 text-white hover:text-blue-300 transition-colors">
                      <HiOutlineShare className="w-6 h-6" />
                      <span className="text-sm">分享</span>
                    </button>
                  </div>
                  <div className="text-sm text-white/80">
                    {commentsData?.nodes?.length || 0} 条评论
                  </div>
                </div>
              </div>
            </div>

            {/* 右侧：用户信息和评论区域 (35%) */}
            <div className="w-full md:w-[35%] flex flex-col bg-white">
              {/* 用户信息区域 */}
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-300 rounded-full overflow-hidden">
                      {post.author?.node?.avatar?.url ? (
                        <Image
                          src={post.author.node.avatar.url}
                          alt={post.author.node.name || '用户头像'}
                          width={40}
                          height={40}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-500">
                          <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {post.author?.node?.nickname || post.author?.node?.name || '匿名用户'}
                      </h3>
                      <p className="text-xs text-gray-500">
                        {new Date(post.date).toLocaleDateString('zh-CN')}
                      </p>
                    </div>
                  </div>
                  <button className="px-4 py-1 text-sm bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors">
                    关注
                  </button>
                </div>

                {/* 笔记标题和描述 */}
                <h2 className="font-bold text-lg mb-2 text-gray-900">{post.title}</h2>
                <div className="text-sm text-gray-700 line-clamp-3">
                  <PostContentSmart
                    postId={post.databaseId || 0}
                    initialContent={post.content || ''}
                    postTitle={post.title}
                    isUnlocked={true}
                    graphqlSingleName={post.__typename}
                  />
                </div>
              </div>

              {/* 评论区域 */}
              <div className="flex-1 overflow-y-auto">
                <CommentSection
                  postId={post.databaseId || 0}
                  commentStatus={post.commentStatus || 'closed'}
                  isCustomType={true}
                  initialData={{
                    nodes: commentsData?.nodes ?? [],
                    pageInfo: commentsData?.pageInfo ?? { hasNextPage: false, endCursor: null },
                  }}
                />
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

const NoteModalPage = () => {
  const searchParams = useSearchParams();
  const isModal = searchParams.get('modal') === 'true';

  // 使用 Suspense 是因为 useSearchParams() 可能会在初始渲染时挂起
  // 这是一个好习惯，可以防止潜在的渲染问题
  return (
    <Suspense fallback={null}>
      {isModal ? <ModalContent /> : null}
    </Suspense>
  );
};

export default NoteModalPage; 