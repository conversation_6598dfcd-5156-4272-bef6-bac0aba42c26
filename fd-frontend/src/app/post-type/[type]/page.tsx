import React from 'react';
import { Metadata } from 'next';
import Link from 'next/link';
import MainLayout from '@/components/layouts/MainLayout';
import CustomPostListView from '@/components/CustomPostListView';
import { buildDynamicPostQuery } from '@/lib/graphql-utils';
import { getSiteSettings, getRoutePrefixes, getCustomTypeViewSettings, getCptInfo, getCptInitialData } from '@/lib/api';

// GraphQL端点
const GRAPHQL_ENDPOINT = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'https://admin.futuredecade.com/graphql';

// 定义SEO数据类型
interface CustomPostTypeSeoData {
  aiSeoTitle?: string;
  aiSeoDescription?: string;
  aiSeoJsonLd?: string;
  enabled?: boolean;
  lastUpdated?: string;
}

// Helper to convert CPT slug to GraphQL Type Name (e.g., "note" -> "Note")
function toGraphQLTypeName(slug: string): string {
  if (!slug) return '';
  return slug.split('-').map(part => part.charAt(0).toUpperCase() + part.slice(1)).join('');
}

// 通过自省查询获取ACF字段信息
async function getAcfFieldsForType(type: string) {
  const gqlTypeName = toGraphQLTypeName(type);
  if (!gqlTypeName) {
    return { acfFields: [], acfContainerTypeName: null };
  }

  const runQuery = async (query: string, variables: Record<string, any>) => {
    const response = await fetch(
      process.env.NEXT_PUBLIC_WORDPRESS_API_URL || '',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query, variables }),
        next: { revalidate: 3600 }, // 自省查询结果可以长时间缓存
      }
    );
    const result = await response.json();
    if (result.errors) {
      console.error('ACF Introspection Error:', result.errors);
      return null;
    }
    return result.data;
  };

  try {
    const typeIntrospectionQuery = `
      query IntrospectPostType($typeName: String!) {
        __type(name: $typeName) {
          fields {
            name
            type { name, kind }
          }
        }
      }
    `;
    const typeData = await runQuery(typeIntrospectionQuery, { typeName: gqlTypeName });
    const acfContainerField = typeData?.__type?.fields?.find(
      (field: any) => field.name.toLowerCase().endsWith('fields') && field.type.kind === 'OBJECT'
    );

    if (!acfContainerField) {
      return { acfFields: [], acfContainerTypeName: null };
    }
    
    const acfContainerTypeName = acfContainerField.type.name;

    const acfFieldsQuery = `
      query IntrospectAcfContainerType($typeName: String!) {
        __type(name: $typeName) {
          fields {
            name
            description
            type { name, kind }
          }
        }
      }
    `;
    const acfFieldsData = await runQuery(acfFieldsQuery, { typeName: acfContainerTypeName });
    const acfFields = acfFieldsData?.__type?.fields || [];
    
    return { acfFields, acfContainerTypeName };

  } catch (error) {
    console.error(`Error fetching ACF fields for type ${type}:`, error);
    return { acfFields: [], acfContainerTypeName: null };
  }
}

// 使用动态查询获取文章列表
async function getDynamicPostsList(query: string, type: string, first: number, after: string | null = null) {
  try {
    const variables = {
      first,
      after,
      contentTypes: [type.toUpperCase()],
    };
    
    const response = await fetch(
      process.env.NEXT_PUBLIC_WORDPRESS_API_URL || '',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query,
          variables
        }),
        next: { revalidate: 300 } // 5分钟重验证
      }
    );
    const result = await response.json();
    if (result.errors) {
      console.error('Dynamic Post Fetch Error:', result.errors);
      return null;
    }
    
    return result.data?.contentNodes;
  } catch(e) {
    console.error('Network error fetching dynamic posts:', e);
    return null;
  }
}

/**
 * 获取自定义类型页面SEO数据
 */
async function fetchCustomPostTypeSeoData(postType: string): Promise<CustomPostTypeSeoData | null> {
  try {
    const query = `
      query GetCustomPostTypeSeoData($postType: String!) {
        customPostTypeSeoData(postType: $postType) {
          aiSeoTitle
          aiSeoDescription
          aiSeoJsonLd
          enabled
          lastUpdated
        }
      }
    `;

    const response = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query,
        variables: { postType }
      }),
      next: {
        revalidate: 600, // ISR: 10分钟
        tags: [`custom-post-type-seo:${postType}`] // 缓存标签
      },
    } as RequestInit & { next: { revalidate: number; tags: string[] } });

    const json = await response.json();
    if (json.errors) {
      console.error('GraphQL errors:', json.errors);
      return null;
    }

    return json.data?.customPostTypeSeoData || null;
  } catch (error) {
    console.error(`Failed to fetch SEO data for post type ${postType}:`, error);
    return null;
  }
}

/**
 * 清理和解析JSON-LD数据
 */
function cleanAndParseJsonLd(rawJsonLd: string, postType: string): any | null {
  if (!rawJsonLd) return null;

  try {
    let cleanJsonLd = rawJsonLd;

    // 移除markdown代码块标记
    cleanJsonLd = cleanJsonLd.replace(/```json\s*/, '').replace(/```\s*$/, '');

    // 处理转义字符
    cleanJsonLd = cleanJsonLd.replace(/\\\\\\"/g, '"'); // 处理三重转义
    cleanJsonLd = cleanJsonLd.replace(/\\"/g, '"');     // 处理双重转义
    cleanJsonLd = cleanJsonLd.replace(/\\\\/g, '\\');   // 处理反斜杠转义

    // 移除换行符和回车符
    cleanJsonLd = cleanJsonLd.replace(/\r\n/g, '').replace(/\r/g, '').replace(/\n/g, '');

    // 尝试解析JSON
    const jsonLd = JSON.parse(cleanJsonLd);

    // 更新URL为正确的前端URL
    if (jsonLd.url) {
      jsonLd.url = jsonLd.url.replace('admin.futuredecade.com', 'www.futuredecade.com');
      // 确保自定义类型页面URL正确
      if (jsonLd.url.includes('/post-type/')) {
        jsonLd.url = `https://www.futuredecade.com/${postType}`;
      }
    }

    console.log(`Successfully parsed JSON-LD for ${postType}:`, jsonLd);
    return jsonLd;
  } catch (error) {
    console.error(`Failed to parse JSON-LD for ${postType}:`, error);
    return null;
  }
}

// 获取分类列表（服务器端）
async function fetchCategoriesForType(postType: string, first: number = 100) {
  const fieldName = `${postType}Categories`;
  const query = `query GetCats($first:Int){\n  ${fieldName}(first:$first){\n    nodes{ id name slug count }\n  }\n}`;

  const res = await fetch(process.env.NEXT_PUBLIC_WORDPRESS_API_URL || '', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ query, variables: { first } }),
    next: { revalidate: 300 },
  } as RequestInit & { next: { revalidate: number } });

  const json = await res.json();
  return json.data && json.data[fieldName] ? json.data[fieldName].nodes : [];
}

// 设置ISR模式，但使用较短的重验证时间（5分钟）
export const revalidate = 300;

// 设置动态元数据
export async function generateMetadata({ params }: { params: { type: string } }): Promise<Metadata> {
  const { type } = params;

  // 获取SEO数据和站点设置
  const [seoData, settings] = await Promise.all([
    fetchCustomPostTypeSeoData(type),
    getSiteSettings(),
  ]);

  const metaTitle = seoData?.aiSeoTitle || `${type.charAt(0).toUpperCase() + type.slice(1)} - ${settings?.title || 'Future Decade'}`;
  const metaDescription = seoData?.aiSeoDescription || `浏览我们的${type}内容列表 - ${settings?.description || ''}`;
  const canonicalUrl = `https://www.futuredecade.com/${type}`;

  return {
    title: metaTitle,
    description: metaDescription,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      url: canonicalUrl,
      siteName: '未来学人',
      type: 'website',
      locale: 'zh_CN',
      images: [
        {
          url: 'https://www.futuredecade.com/images/default-og-image.jpg',
          width: 1200,
          height: 630,
          alt: metaTitle,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      images: ['https://www.futuredecade.com/images/default-og-image.jpg'],
      site: '@FutureDecade',
      creator: '@FutureDecade',
    },
  };
}

// 自定义类型列表页 (动态版)
export default async function CustomPostTypePage({
  params,
  searchParams,
}: {
  params: { type: string };
  searchParams?: { cat?: string };
}) {
  const { type } = params;
  const activeCat = searchParams?.cat || '';

  // 1. 新方法：获取CPT信息（包含预构建的Fragment）
  console.log(`[CPT Debug] Starting data fetch for type: ${type}`);
  const cptInfo = await getCptInfo(type);
  console.log(`[CPT Debug] CPT Info result:`, cptInfo);

  let initialPosts: any[] = [];
  let initialPageInfo: any = { hasNextPage: false, endCursor: null };
  let finalQuery: string = '';

  // 临时强制使用旧方法，因为新方法的Fragment缺少作者和反应数据
  // TODO: 修复后端Fragment构建函数后，可以恢复新方法
  if (!cptInfo.isValid || true) { // 强制使用旧方法
    try {
      // 保持原有逻辑作为降级方案
      const { acfFields, acfContainerTypeName } = await getAcfFieldsForType(type);

      const dynamicQuery = buildDynamicPostQuery(type, acfContainerTypeName, acfFields);

      const initialData = await getDynamicPostsList(dynamicQuery, type, 12);

      initialPosts = initialData?.nodes || [];
      initialPageInfo = initialData?.pageInfo || { hasNextPage: false, endCursor: null };
      finalQuery = dynamicQuery;

    } catch (error) {
      console.error(`[CPT Debug] Legacy method error:`, error);
    }
  } else {
    // 新方法：使用预构建的Fragment
    const { listFragment, cacheTag } = cptInfo;
    console.log(`[CPT Debug] Using V2 method with cache tag: ${cacheTag}`);

    try {
      // 构建完整的GraphQL查询
      const fragmentName = `${toGraphQLTypeName(type)}ListFields`;
      const fullQuery = `
        ${listFragment}

        query GetCptList($first: Int, $after: String) {
          contentNodes(
            first: $first,
            after: $after,
            where: { contentTypes: [${type.toUpperCase()}] }
          ) {
            nodes {
              ...${fragmentName}
            }
            edges {
              cursor
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      `;

      // 获取初始数据
      const initialData = await getCptInitialData(type, fullQuery, 12);

      initialPosts = initialData?.nodes || [];
      initialPageInfo = initialData?.pageInfo || { hasNextPage: false, endCursor: null };
      finalQuery = fullQuery;

    } catch (error) {
      console.error(`[CPT Debug] V2 method error:`, error);
    }
  }

  // 3. 并行获取路由设置、视图设置和SEO数据
  const [routePrefixes, viewSettings, seoData, categories] = await Promise.all([
    getRoutePrefixes(),
    getCustomTypeViewSettings(),
    fetchCustomPostTypeSeoData(type),
    fetchCategoriesForType(type),
  ]);

  const typeViewSetting = viewSettings.find((setting: any) => setting.typeName === type);
  const viewComponent = typeViewSetting?.viewComponent || 'default';

  // 4. 准备JSON-LD结构化数据
  let jsonLd = null;
  if (seoData?.aiSeoJsonLd) {
    jsonLd = cleanAndParseJsonLd(seoData.aiSeoJsonLd, type);
  }
  
  if (initialPosts.length === 0) {
    return (
      <>
        {jsonLd && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
          />
        )}
        <MainLayout>
          <div className="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              {`${type.charAt(0).toUpperCase() + type.slice(1)}`}
            </h1>
            <p className="mt-4 text-lg text-gray-500">
              此类别下暂无内容。我们正在努力更新中，请稍后再来查看。
            </p>
            <div className="mt-6">
              <Link href="/" className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                返回首页
                      </Link>
            </div>
          </div>
        </MainLayout>
      </>
    );
  }

  return (
    <>
      {jsonLd && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
      )}
      <MainLayout>
        <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
          <div className="mb-6">
            <h1 className="text-4xl font-extrabold tracking-tight text-gray-900 sm:text-5xl">
                {cptInfo.name || (type.charAt(0).toUpperCase() + type.slice(1))}
            </h1>
            <p className="text-gray-600">浏览我们的{type}内容列表</p>
          </div>
          <CustomPostListView
            key={activeCat}
            initialPosts={initialPosts}
            initialPageInfo={initialPageInfo}
            postType={type}
            query={finalQuery}
            viewComponent={viewComponent}
            routePrefixes={routePrefixes}
            categories={categories}
          />
        </div>
      </MainLayout>
    </>
  );
}