// @ts-nocheck
import React from 'react';
import { notFound, redirect } from 'next/navigation';
import { getCustomPostByUuid, getFdMemberSettings, getCommentsForNode } from '@/lib/api';
import { buildCustomPostUrl } from '@/utils/url-builder';
import dynamic from 'next/dynamic';
import ReactionButton from '@/components/post/ReactionButton';
import { ReactionType } from '@/types/Reaction';
import { Post as PostType } from '@/types/post';
import { HiOutlineHeart, HiOutlineBookmark, HiOutlineThumbUp } from 'react-icons/hi';

const CommentSection = dynamic(() => import('@/components/comments/CommentSection'), {
  loading: () => <div className="text-center p-8">加载评论区...</div>,
});
import MainLayout from '@/components/layouts/MainLayout';
import ShareButtons from '@/components/share/ShareButtons';
import PostContentSmart from '@/components/post/PostContentSmart';
import NoteImageDisplay from '@/components/note/NoteImageDisplay';
import Script from 'next/script';

// 设置ISR模式，60分钟重新验证
export const revalidate = 3600;

// 自定义文章类型接口
interface CustomPost {
  id: string;
  __typename: string;
  title: string;
  slug: string;
  excerpt?: string;
  content?: string;
  date: string;
  databaseId?: number;
  shortUuid?: string;
  commentStatus?: string;
  likesCount?: number;
  userHasLiked?: boolean;
  recommendsCount?: number;
  userHasRecommended?: boolean;
  bookmarksCount?: number;
  userHasBookmarked?: boolean;
  shareImage?: string;
  featuredImageUrl?: string;
  featuredImage?: {
    node: {
      sourceUrl: string;
      altText?: string;
    }
  };
  author?: {
    node: {
      id: string;
      name: string;
      slug: string;
    }
  };
  unlockPrice?: number;
  requiredMemberLevel?: number;
  isUnlockedByCurrentUser?: boolean;
  aiSeoTitle?: string;
  aiSeoDescription?: string;
  aiSeoJsonLd?: string;
  comments?: any[]; // Added for initialData prop
}

/**
 * 比较两个slug是否匹配，考虑编码差异
 * @param slug1 第一个slug
 * @param slug2 第二个slug
 * @returns 是否匹配
 */
function slugsMatch(slug1: string, slug2: string): boolean {
  // 解码用于比较的slug
  const decoded1 = decodeURIComponent(slug1);
  const decoded2 = decodeURIComponent(slug2);
  
  // 比较解码后的slug
  if (decoded1 === decoded2) {
    return true;
  }
  
  // 检查是否包含中文字符
  const hasChinese = /[\u4e00-\u9fa5]/.test(decoded1) || /[\u4e00-\u9fa5]/.test(decoded2);
  
  // 对于中文slug，进行更宽松的比较
  if (hasChinese) {
    // 如果长度相似，视为匹配以避免重定向循环
    const lengthDifference = Math.abs(decoded1.length - decoded2.length);
    if (lengthDifference <= 5) {
      return true;
    }
    
    // 移除常见标点和空格后比较
    const clean1 = decoded1.replace(/[-_《》【】\s'"：，。、？！（）()]/g, '');
    const clean2 = decoded2.replace(/[-_《》【】\s'"：，。、？！（）()]/g, '');
    
    if (clean1 === clean2) {
      return true;
    }
  }
  
  return false;
}

// 设置动态元数据
export async function generateMetadata({ params }: { params: { type: string, uuid: string, slug: string } }) {
  const { type, uuid, slug } = params;
  const post = await getCustomPostByUuid(type, uuid);

  if (!post) return { title: '内容未找到' };

  const cleanExcerpt = post.excerpt ? post.excerpt.replace(/<[^>]*>?/gm, '') : '';
  const metaTitle = post.aiSeoTitle || post.title;
  const metaDescription = post.aiSeoDescription || cleanExcerpt || '';

  // 获取路由前缀用于构建canonical URL
  let customTypePrefix = 'post-type';
  try {
    const { getRoutePrefixes } = await import('@/lib/route-prefixes');
    const routePrefixes = await getRoutePrefixes();
    customTypePrefix = routePrefixes.customTypePrefix || 'post-type';
  } catch (error) {
    console.error('Error fetching route prefixes for metadata:', error);
  }

  const canonicalUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://www.futuredecade.com'}/${customTypePrefix}/${type}/${uuid}/${slug}`;

  return {
    title: `${metaTitle} - Future Decade`,
    description: metaDescription,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      url: canonicalUrl,
      siteName: 'Future Decade',
      type: 'article',
      images: post.featuredImage?.node?.sourceUrl ? [
        {
          url: post.featuredImage.node.sourceUrl,
          width: 1200,
          height: 630,
          alt: post.featuredImage.node.altText || metaTitle,
        }
      ] : [],
      locale: 'zh_CN',
    },
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      images: post.featuredImage?.node?.sourceUrl ? [post.featuredImage.node.sourceUrl] : [],
      site: '@FutureDecade',
      creator: '@FutureDecade',
    },
  };
}

export default async function CustomPostTypePage({ params }: { 
  params: { type: string; uuid: string; slug: string } 
}) {
  const { type, uuid, slug } = params;

  console.log('--- [PAGE DEBUG] CustomPostTypePage ---');
  console.log('Params received:', JSON.stringify(params, null, 2));

  // Step 1: Fetch main post data
  const post = await getCustomPostByUuid(type, uuid) as PostType;
  
  if (!post) {
    console.error('[PAGE DEBUG] Post data is null, returning 404.');
    return notFound();
  }

  // Step 2: Fetch settings and comments in parallel
  const [settings, commentsData] = await Promise.all([
    getFdMemberSettings(),
    post.commentStatus !== 'closed' 
      ? getCommentsForNode(post.databaseId, type) // 传递 type
      : Promise.resolve(null)
  ]);

  console.log('Data fetched for post:', JSON.stringify(post, null, 2));
  console.log('Data fetched for comments:', JSON.stringify(commentsData, null, 2));
  console.log('--- [PAGE DEBUG] End ---');
  
  // 获取当前的路由前缀设置
  let routePrefixes = {
    postPrefix: 'articles',
    categoryPrefix: null,
    tagPrefix: 'topics',
    categoryIndexRoute: 'category-index',
    tagIndexRoute: 'tag-index',
    customTypePrefix: 'post-type'
  };

  try {
    const { getRoutePrefixes } = await import('@/lib/route-prefixes');
    const fetchedPrefixes = await getRoutePrefixes();
    routePrefixes = { ...routePrefixes, ...fetchedPrefixes };
  } catch (error) {
    console.error('Error fetching route prefixes:', error);
  }
  
  // 检查slug是否匹配，同时考虑编码差异
  if (!slugsMatch(post.slug, slug)) {
    try {
      // 尝试重定向到正确的URL
      // 检查是否包含中文字符
      const hasChinese = /[\u4e00-\u9fa5]/.test(decodeURIComponent(post.slug));
      
      // 对于中文slug，尽量避免重定向，除非完全不同
      if (!hasChinese || slug.length < post.slug.length / 2) {
        // 使用URL构建函数重定向
        return redirect(buildCustomPostUrl(type, uuid, encodeURIComponent(post.slug), routePrefixes));
      }
    } catch (error) {
      // 如果重定向出错，继续显示页面而不是显示错误
      console.error('Redirect error:', error);
    }
  }
  
  // 判断是否为笔记类型
  const isNoteType = type === 'note';

  return (
    <MainLayout>
      <article className={`article-container mx-auto py-10 px-4 ${isNoteType ? 'max-w-5xl' : 'max-w-4xl'}`}>
        {/* 注入 JSON-LD */}
        {post.aiSeoJsonLd && (
          <Script
            id="cpt-jsonld"
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: post.aiSeoJsonLd }}
          />
        )}

        {/* 笔记类型的特殊布局 */}
        {isNoteType ? (
          <>
            {/* 笔记标题和作者信息 */}
            <div className="flex items-center justify-between mb-6">
              <h1 className="text-3xl font-bold">{post.title}</h1>
              <div className="flex items-center space-x-3">
                {post.author?.node && (
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gray-300 rounded-full overflow-hidden mr-2">
                      {post.author.node.avatar?.url ? (
                        <img
                          src={post.author.node.avatar.url}
                          alt={post.author.node.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-500">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                    <span className="text-gray-700">{post.author.node.name}</span>
                  </div>
                )}
                <span className="text-gray-500 text-sm">
                  {new Date(post.date).toLocaleDateString('zh-CN')}
                </span>
              </div>
            </div>

            {/* 图片轮播区域 */}
            <div className="mb-8 rounded-lg overflow-hidden shadow-md" style={{ height: '500px' }}>
              <NoteImageDisplay
                featuredImage={post.featuredImage?.node}
                content={post.content || ''}
                showThumbnails={true}
              />
            </div>
          </>
        ) : (
          <>
            <h1 className="text-3xl font-bold mb-6">{post.title}</h1>

            {/* 内容头部信息 */}
            <div className="article-meta mb-8">
              <div className="flex items-center text-gray-600 mb-4">
                <span className="mr-4">
                  发布于: {new Date(post.date).toLocaleDateString('zh-CN')}
                </span>
                {post.author?.node && (
                  <span>作者: {post.author.node.name}</span>
                )}
              </div>
            </div>

            {/* 特色图片 */}
            {post.featuredImage?.node?.sourceUrl && (
              <div className="featured-image mb-8">
                <img
                  src={post.featuredImage.node.sourceUrl}
                  alt={post.featuredImage.node.altText || post.title}
                  className="w-full rounded-lg shadow-md"
                />
              </div>
            )}
          </>
        )}
        
        {/* 内容 */}
        <div 
          className="article-content"
        >
          <PostContentSmart
            postId={post.databaseId || 0}
            initialContent={post.content || ''}
            postTitle={post.title}
            unlockPrice={post.unlockPrice}
            requiredMemberLevel={post.requiredMemberLevel}
            isUnlocked={!!post.isUnlockedByCurrentUser}
            paywallVariant={settings.paywallVariant}
            graphqlSingleName={post.__typename}
        />
        </div>
        
        {/* 分享按钮（阶段 2：仅渲染按钮，基于其他平台） */}
        <div className="max-w-4xl mx-auto px-4">
          <ShareButtons
            postId={(post.databaseId || 0).toString()}
            postTitle={post.title}
            postUrl={`${process.env.NEXT_PUBLIC_SITE_URL || ''}${buildCustomPostUrl(type, uuid, post.slug, routePrefixes)}`}
            postExcerpt={post.excerpt || ''}
            shareImage={post.shareImage}
          />
        </div>
        
        {/* 点赞与推荐按钮容器 */}
        <div className="my-8 flex justify-center items-center space-x-6">
          <ReactionButton
            postId={post.id}
            initialCount={post.likeCount || 0}
            initialState={post.userHasLiked || false}
            type={ReactionType.Like}
            icon={<HiOutlineHeart className="w-5 h-5 mr-2" />}
            activeClass="bg-rose-500 text-white hover:bg-rose-600"
            inactiveClass="bg-gray-100 text-gray-700 hover:bg-gray-200"
            successMessage="感谢点赞！"
            undoMessage="已取消点赞"
          />
          <ReactionButton
            postId={post.id}
            initialCount={post.bookmarkCount || 0}
            initialState={post.userHasBookmarked || false}
            type={ReactionType.Bookmark}
            icon={<HiOutlineBookmark className="w-5 h-5 mr-2" />}
            activeClass="bg-blue-500 text-white hover:bg-blue-600"
            inactiveClass="bg-gray-100 text-gray-700 hover:bg-gray-200"
            successMessage="已收藏"
            undoMessage="已取消收藏"
          />
          <ReactionButton
            postId={post.id}
            initialCount={post.recommendCount || 0}
            initialState={post.userHasRecommended || false}
            type={ReactionType.Recommend}
            icon={<HiOutlineThumbUp className="w-5 h-5 mr-2" />}
            activeClass="bg-green-500 text-white hover:bg-green-600"
            inactiveClass="bg-gray-100 text-gray-700 hover:bg-gray-200"
            successMessage="已推荐"
            undoMessage="已取消推荐"
          />
        </div>
        
        {/* 评论区 */}
        <CommentSection
          postId={post.databaseId || 0}
          commentStatus={post.commentStatus || 'closed'}
          isCustomType={true}
          initialData={{
            nodes: commentsData?.nodes ?? [],
            pageInfo: commentsData?.pageInfo ?? { hasNextPage: false, endCursor: null },
          }}
        />
      </article>
    </MainLayout>
  );
} 