// @ts-nocheck
'use client';
import React, { useEffect, useState, useMemo } from 'react';
import { gql, useQuery } from '@apollo/client';
import { useAuthContext } from '@/hooks/useAuthContext';
import PaywallRenderer from './PaywallRenderer';

interface PaywallInfo {
  hasPaywall: boolean;
  previewContent: string;
  loginUrl: string;
  registerUrl: string;
  upgradeUrl: string;
  message: string;
  isLoggedIn: boolean;
}

interface PostContentSmartProps {
  postId: number; // WordPress databaseId
  initialContent: string;
  postTitle?: string;
  unlockPrice?: number;
  requiredMemberLevel?: number;
  isUnlocked?: boolean;
  paywallVariant?: 'default' | 'compact';
  graphqlSingleName?: string; // CPT的GraphQL单数名称，例如 'Post' 或 'Note'
  // 新增：预处理的付费墙信息
  initialPaywallInfo?: PaywallInfo | null;
}

// GraphQL 查询生成函数
const buildPostContentQuery = (graphqlSingleName: string) => {
  // 基础查询部分
  const baseQuery = `
    query GetContentNodeContent($id: ID!) {
      contentNode(id: $id, idType: DATABASE_ID) {
        ... on NodeWithContentEditor {
          content(format: RENDERED)
        }
        ... on NodeWithTitle {
          title
        }
  `;

  // 如果类型名有效，则添加特定类型的片段
  const typeFragment = graphqlSingleName
    ? `
        ... on ${graphqlSingleName} {
          unlockPrice
          requiredMemberLevel
          isUnlockedByCurrentUser
          paywallInfo {
            hasPaywall
            previewContent
            loginUrl
            registerUrl
            upgradeUrl
            message
            isLoggedIn
          }
        }
      `
    : '';

  // 闭合查询
  const closingBrace = `
      }
    }
  `;

  return gql`${baseQuery}${typeFragment}${closingBrace}`;
};

/**
 * 根据用户认证状态，决定是否重新拉取文章内容。
 * 如果初始内容包含付费墙标记，而用户已登录且有权限，再次请求以获得完整内容。
 * 使用精致的付费墙卡片替代原始HTML。
 */
const PostContentSmart: React.FC<PostContentSmartProps> = ({
  postId,
  initialContent,
  postTitle = '',
  unlockPrice = 0,
  requiredMemberLevel = 0,
  isUnlocked = false,
  paywallVariant = 'default',
  graphqlSingleName = 'Post', // 默认为 'Post' 以兼容旧用法
  initialPaywallInfo = null
}) => {
  const { isAuthenticated, user } = useAuthContext();
  const [content, setContent] = useState<string>(initialContent);
  const [postData, setPostData] = useState({
    title: postTitle,
    unlockPrice,
    requiredMemberLevel,
    isUnlocked
  });
  const [paywallInfo, setPaywallInfo] = useState<PaywallInfo | null>(initialPaywallInfo);

  // 当父组件传入的 props 发生变化时，同步更新本地 state
  useEffect(() => {
    setContent(initialContent);
    setPostData({
      title: postTitle,
      unlockPrice,
      requiredMemberLevel,
      isUnlocked
    });
  }, [initialContent, postTitle, unlockPrice, requiredMemberLevel, isUnlocked]);

  // 检测初始内容是否包含付费墙提示 div
  const hasPaywall = initialContent.includes('fd-member-access-denied');

  // 使用 useMemo 动态构建查询，仅在 graphqlSingleName 变化时重新构建
  const POST_CONTENT_QUERY = useMemo(
    () => buildPostContentQuery(graphqlSingleName),
    [graphqlSingleName]
  );

  const { data, loading, refetch } = useQuery(POST_CONTENT_QUERY, {
    variables: { id: postId },
    skip: !isAuthenticated || !hasPaywall, // 仅在已登录且初始内容受限时再查询
    fetchPolicy: 'network-only',
  });

  // 当从GraphQL获取到新的内容后，更新 state
  useEffect(() => {
    if (data?.contentNode) {
      const node = data.contentNode;
      setContent(node.content ?? content);
      setPostData({
        title: node.title || postTitle,
        unlockPrice: node.unlockPrice ?? unlockPrice,
        requiredMemberLevel: node.requiredMemberLevel ?? requiredMemberLevel,
        isUnlocked: node.isUnlockedByCurrentUser ?? isUnlocked,
      });
      // 设置付费墙信息
      setPaywallInfo(node.paywallInfo);
    }
  }, [data, content, postTitle, unlockPrice, requiredMemberLevel, isUnlocked]);

  // 监听用户会员等级变化，自动重新查询内容
  useEffect(() => {
    if (isAuthenticated && hasPaywall && user?.memberLevel) {
      console.log('用户会员等级变化，重新查询文章内容');
      refetch().catch(error => {
        console.error('重新查询文章内容失败:', error);
      });
    }
  }, [user?.memberLevel?.id, isAuthenticated, hasPaywall, refetch]);

  // 监听窗口焦点，当用户从其他页面返回时重新检查内容
  useEffect(() => {
    const handleFocus = () => {
      if (isAuthenticated && hasPaywall) {
        console.log('窗口重新获得焦点，检查文章解锁状态');
        refetch().catch(error => {
          console.error('重新检查文章状态失败:', error);
        });
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [isAuthenticated, hasPaywall, refetch]);

  // 处理文章解锁成功后的回调
  const handleUnlockSuccess = () => {
    console.log('文章解锁成功，重新获取文章数据');
    // 重新获取文章数据，使用network-only确保获取最新数据
    refetch().then(() => {
      console.log('文章数据重新获取完成');
    }).catch(error => {
      console.error('重新获取文章数据失败:', error);
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center space-x-2 text-gray-600">
          <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <span>加载中…</span>
        </div>
      </div>
    );
  }

  return (
    <PaywallRenderer
      content={content || ''}
      postId={postId}
      postTitle={postData.title}
      unlockPrice={postData.unlockPrice}
      requiredMemberLevel={postData.requiredMemberLevel}
      isUnlocked={postData.isUnlocked}
      variant={paywallVariant}
      onUnlock={handleUnlockSuccess}
      paywallInfo={paywallInfo}
      legacyMode={false}
      showLoading={false}
    />
  );
};

export default PostContentSmart; 