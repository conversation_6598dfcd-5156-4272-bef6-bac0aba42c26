'use client';

import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import { toast } from 'react-hot-toast';
import { REACTION_MUTATION, REMOVE_REACTION_MUTATION } from '@/lib/graphql/mutations';
import { ReactionType } from '@/types/Reaction';

interface ReactionButtonProps {
  postId: string; // 全局 ID
  initialCount: number;
  initialState: boolean;
  type: ReactionType;
  icon: JSX.Element;
  activeClass: string;
  inactiveClass: string;
  successMessage: string;
  undoMessage: string;
}

export default function ReactionButton({
  postId,
  initialCount,
  initialState,
  type,
  icon,
  activeClass,
  inactiveClass,
  successMessage,
  undoMessage,
}: ReactionButtonProps) {
  const [count, setCount] = useState(initialCount);
  const [isActive, setIsActive] = useState(initialState);
  const [isLoading, setIsLoading] = useState(false);

  const [addReaction] = useMutation(REACTION_MUTATION);
  const [removeReaction] = useMutation(REMOVE_REACTION_MUTATION);

  const handleClick = async () => {
    if (isLoading) return;
    setIsLoading(true);

    const originalCount = count;
    const originalState = isActive;

    // Optimistic update
    setIsActive(!originalState);
    setCount(originalState ? originalCount - 1 : originalCount + 1);

    try {
      const mutation = !originalState ? addReaction : removeReaction;
      await mutation({ variables: { postId, type } });
      toast.success(!originalState ? successMessage : undoMessage);
    } catch (error) {
      toast.error('操作失败，请重试');
      // Rollback
      setIsActive(originalState);
      setCount(originalCount);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleClick}
      disabled={isLoading}
      className={`reaction-button flex items-center px-4 py-2 rounded-full font-medium transition-colors ${
        isActive ? activeClass : inactiveClass
      } ${isLoading ? 'cursor-not-allowed opacity-70' : ''}`}
    >
      {icon}
      {count}
    </button>
  );
} 