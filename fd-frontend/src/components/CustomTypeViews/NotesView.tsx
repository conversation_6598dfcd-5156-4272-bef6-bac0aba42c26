import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { CustomTypeViewProps, CustomTypePost } from './BaseTypeView';
import { buildCustomPostUrl } from '@/utils/url-builder';

// 假设的图标组件，实际项目中需要替换为真实的图标库，如 react-icons
const HeartIcon = () => (
  <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
  </svg>
);

const NotesView: React.FC<CustomTypeViewProps> = ({ posts, type, routePrefixes }) => {

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
      {posts.map(post => {
          // 优先使用shortUuid，其次使用databaseId
          const uuid = post.shortUuid || post.databaseId?.toString() || '';
          const postUrl = buildCustomPostUrl(type, uuid, post.slug, routePrefixes);
        const modalUrl = `${postUrl}?modal=true`;
          
          return (
          <Link href={modalUrl} key={post.id} className="block group">
            
            {/* 1. 图片容器: 只对图片应用圆角和溢出隐藏 */}
            <div className="relative w-full rounded-lg overflow-hidden">
              {post.featuredImage?.node?.sourceUrl ? (
                <>
                  {/* Aspect ratio box */}
                  <div style={{ paddingTop: '125%' }} />
                  <Image
                    src={post.featuredImage.node.sourceUrl}
                    alt={post.featuredImage.node.altText || post.title}
                    layout="fill"
                    objectFit="cover"
                    className="absolute top-0 left-0 w-full h-full group-hover:opacity-90 transition-opacity"
                  />
                </>
              ) : (
                <div className="relative w-full bg-gray-200 rounded-lg" style={{ paddingTop: '125%' }}>
                  <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                    <p>无图</p>
                  </div>
                      </div>
                    )}
                  </div>

            {/* 2. 文字容器: 独立于图片容器 */}
            <div className="pt-3">
              <h3 className="text-sm font-semibold text-gray-900 break-words line-clamp-2 mb-2">
                {post.title}
              </h3>
              <div className="flex items-center justify-between text-xs text-gray-500">
                <div className="flex items-center min-w-0">
                  <div className="w-5 h-5 bg-gray-300 rounded-full mr-2 flex-shrink-0"></div>
                  <span className="truncate">笔记作者</span>
                </div>
                <div className="flex items-center flex-shrink-0">
                  <HeartIcon />
                  <span className="ml-1">1k+</span>
                </div>
              </div>
            </div>

          </Link>
          );
        })}
    </div>
  );
};

export default NotesView; 