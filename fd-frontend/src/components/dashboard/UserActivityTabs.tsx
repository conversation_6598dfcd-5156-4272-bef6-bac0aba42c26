'use client';

import { useState, useContext } from 'react';
import { useQuery, gql, useMutation } from '@apollo/client';
import Link from 'next/link';
import { Loader2, ThumbsUp, Heart, Bookmark, FileText, Trash2 } from 'lucide-react';
import { useRoutePrefixes } from '@/hooks/useRoutePrefixes';
import { buildPostUrl } from '@/utils/url-builder';
import { AuthContext } from '@/contexts/AuthContext';
import { ReactionType } from '@/types/Reaction';
import { GET_USER_REACTIONS } from '@/lib/graphql/queries';

const USER_SUBMISSIONS_QUERY = gql`
  query GetUserSubmissions($limit: Int!, $offset: Int!) {
    userSubmissions(limit: $limit, offset: $offset) {
      id
      databaseId
      title
      slug
      shortUuid
      date
      status
      contentType {
        node {
          name
        }
      }
    }
  }
`;

const WITHDRAW_SUBMISSION_MUTATION = gql`
  mutation WithdrawPostSubmission($id: ID!) {
    withdrawPostSubmission(input: { id: $id }) {
      post {
        id
        status
      }
    }
  }
`;

const DELETE_MY_POST_MUTATION = gql`
    mutation DeleteMyPost($id: ID!) {
        deleteMyPost(input: { id: $id }) {
            deletedId
        }
    }
`;

const queries = {
  likes: GET_USER_REACTIONS,
  recommends: GET_USER_REACTIONS,
  bookmarks: GET_USER_REACTIONS,
  submissions: USER_SUBMISSIONS_QUERY,
};

const dataKeys = {
  likes: 'userReactions',
  recommends: 'userReactions',
  bookmarks: 'userReactions',
  submissions: 'userSubmissions',
};

type ActivityType = 'likes' | 'recommends' | 'bookmarks' | 'submissions';

interface Post {
    id: string;
    databaseId: number;
    title: string;
    date: string;
    slug: string;
    shortUuid: string;
    status?: string;
    contentType: {
      node: {
        name: string;
      }
    };
}

const POSTS_PER_PAGE = 10;

const PostList = ({ type }: { type: ActivityType }) => {
    const auth = useContext(AuthContext);
    const userRole = auth?.user?.role;
    const isAuthorOrHigher = userRole && ['author', 'editor', 'administrator'].includes(userRole);

    const { loading, error, data, fetchMore, refetch } = useQuery(queries[type], {
        variables: { 
          first: POSTS_PER_PAGE, 
          after: null,
          type: type.toUpperCase() as ReactionType // Pass reaction type for relevant queries
        },
        skip: type === 'submissions', // We can skip this query for submissions initially
        fetchPolicy: 'network-only',
    });
    const { prefixes, loading: prefixesLoading } = useRoutePrefixes();

    const [withdrawSubmission, { loading: withdrawing }] = useMutation(WITHDRAW_SUBMISSION_MUTATION, {
        onCompleted: () => refetch(),
        onError: (err) => alert(`操作失败: ${err.message}`),
    });

    const [deleteMyPost, { loading: deleting }] = useMutation(DELETE_MY_POST_MUTATION, {
        onCompleted: () => {
            refetch();
        },
        onError: (err) => alert(`删除失败: ${err.message}`),
    });

    const getStatusInChinese = (status: string) => {
        switch (status) {
            case 'publish': return '已发布';
            case 'pending': return '审核中';
            case 'draft': return '草稿';
            default: return status;
        }
    };

    if (loading || prefixesLoading) return (
        <div className="flex justify-center items-center py-10">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
    );
    if (error) return <p className="text-red-500 text-center py-10">加载失败: {error.message}</p>;

    const posts = data?.[dataKeys[type]]?.nodes || [];
    const hasMore = data?.[dataKeys[type]]?.pageInfo?.hasNextPage;

    if (posts.length === 0) {
        return <p className="text-gray-500 text-center py-10">这里还没有内容。</p>;
    }

    const getPostUrl = (post: Post) => {
        if (!prefixes || !post.shortUuid || !post.slug) return '#';
        
        const contentTypeName = post.contentType?.node?.name || 'post';
        
        if (contentTypeName && contentTypeName !== 'post') {
            const customType = contentTypeName.toLowerCase();
            const sanitizedSlug = encodeURIComponent(post.slug);
            return `/${customType}/${post.shortUuid}/${sanitizedSlug}`;
        }
        
        return buildPostUrl(post.shortUuid, post.slug, prefixes);
    };

    return (
        <div className="space-y-4">
            {posts.map((post: Post) => (
                <div 
                  key={post.id}
                  className="block bg-white p-4 rounded-lg shadow-sm border border-gray-200"
                >
                  <div className="flex justify-between items-center">
                    <Link href={getPostUrl(post)} className={!prefixes ? 'pointer-events-none' : 'flex-grow'}>
                      <div>
                          <h3 className="font-semibold text-lg text-gray-800 flex items-center group-hover:text-blue-600">
                              {post.title}
                              {type === 'submissions' && post.status && (
                                <span className="ml-2 inline-block text-xs px-2 py-0.5 rounded-full border border-gray-300 text-gray-600">
                                    {getStatusInChinese(post.status)}
                                </span>
                              )}
                          </h3>
                          <p className="text-sm text-gray-500 mt-1">
                              {new Date(post.date).toLocaleDateString('zh-CN')}
                          </p>
                      </div>
                    </Link>
                    
                    {type === 'submissions' && (
                    <div className="flex-shrink-0 ml-4 space-x-4">
                        {isAuthorOrHigher && (
                          <Link href={`/submit?edit=${post.databaseId}`}>
                            <div className="text-sm font-semibold text-blue-600 hover:text-blue-800 inline-block">修改</div>
                          </Link>
                        )}
                        {post.status === 'pending' && !isAuthorOrHigher && (
                          <button
                            onClick={() => {
                              if (confirm('撤回后文章将变为草稿，修改后需要重新提交审核。确定撤回吗？')) {
                                withdrawSubmission({ variables: { id: post.id } });
                              }
                            }}
                            disabled={withdrawing}
                            className="text-sm font-semibold text-yellow-600 hover:text-yellow-800 disabled:opacity-50"
                          >
                            撤回
                          </button>
                        )}
                        {post.status === 'draft' && !isAuthorOrHigher && (
                          <Link href={`/submit?edit=${post.databaseId}`}>
                            <div className="text-sm font-semibold text-blue-600 hover:text-blue-800 inline-block">修改</div>
                          </Link>
                        )}
                        {isAuthorOrHigher && (
                            <button
                                onClick={() => {
                                    if (confirm('确定要删除这篇文章吗？此操作不可撤销。')) {
                                        deleteMyPost({ variables: { id: post.id } });
                                    }
                                }}
                                disabled={deleting}
                                className="text-sm font-semibold text-red-600 hover:text-red-800 disabled:opacity-50 inline-block"
                            >
                                <Trash2 className="inline-block h-4 w-4" /> 删除
                            </button>
                        )}
                    </div>
                    )}
                  </div>
                </div>
            ))}
            {hasMore && (
                <div className="text-center mt-6">
                    <button
                        onClick={() => {
                            fetchMore({
                                variables: { 
                                  first: POSTS_PER_PAGE, 
                                  after: data?.[dataKeys[type]]?.pageInfo?.endCursor,
                                  type: type.toUpperCase() as ReactionType // Pass reaction type for relevant queries
                                },
                                updateQuery: (prevResult, { fetchMoreResult }) => {
                                    if (!fetchMoreResult) return prevResult;
                                    const prevPosts = prevResult[dataKeys[type]]?.nodes || [];
                                    const newPosts = fetchMoreResult[dataKeys[type]]?.nodes || [];
                                    return {
                                        ...prevResult,
                                        [dataKeys[type]]: {
                                            ...prevResult[dataKeys[type]],
                                            nodes: [...prevPosts, ...newPosts],
                                        },
                                    };
                                },
                            });
                        }}
                        className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                    >
                        加载更多
                    </button>
                </div>
            )}
        </div>
    );
};

export const UserActivityTabs = () => {
    const [activeTab, setActiveTab] = useState<ActivityType>('submissions');

    const authTabs = useContext(AuthContext);
    const userRoleTabs = authTabs?.user?.role;
    const isAuthorOrHigherTabs = userRoleTabs && ['author', 'editor', 'administrator'].includes(userRoleTabs);

    const tabs: { id: ActivityType, label: string, icon: React.ReactNode }[] = [
        { id: 'submissions', label: isAuthorOrHigherTabs ? '我的作品' : '我的投稿', icon: <FileText className="w-4 h-4 mr-2" /> },
        { id: 'likes', label: '我的点赞', icon: <ThumbsUp className="w-4 h-4 mr-2" /> },
        { id: 'recommends', label: '我的推荐', icon: <Heart className="w-4 h-4 mr-2" /> },
        { id: 'bookmarks', label: '我的收藏', icon: <Bookmark className="w-4 h-4 mr-2" /> },
    ];

    return (
        <div>
            <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8" aria-label="Tabs">
                    {tabs.map(tab => (
                        <button
                            key={tab.id}
                            onClick={() => setActiveTab(tab.id)}
                            className={`${
                                activeTab === tab.id
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
                            aria-current={activeTab === tab.id ? 'page' : undefined}
                        >
                            {tab.icon} {tab.label}
                        </button>
                    ))}
                </nav>
            </div>
            <div className="py-6">
                <PostList type={activeTab} />
            </div>
        </div>
    );
}; 