'use client';

import React, { useState, useEffect } from 'react';
import { useQuery } from '@apollo/client';
import { useShare } from '@/hooks/useShare';
import { Image as ImageIcon } from 'lucide-react'; // 通用图标
import { FaWeibo, Fa<PERSON><PERSON>xin, FaQq } from 'react-icons/fa'; // 品牌图标
import { FaXTwitter } from 'react-icons/fa6'; // 导入X图标
import ShareModal from './ShareModal';
import { QRCodeSVG } from 'qrcode.react'; // 正确导入二维码组件
import { GET_WECHAT_CONFIG } from '@/lib/graphql/share';

interface ShareButtonsProps {
  postId: string;
  postTitle: string;
  postUrl: string;
  postExcerpt?: string;
  shareImage?: string; // 使用后端处理过的分享图片
}

interface WeChatConfigData {
  wechatSdkConfig: {
    appId: string;
    timestamp: string;
    nonceStr: string;
    signature: string;
  };
}

// 微信分享钩子，配置JS-SDK并提供分享方法
const useWeChatShare = (postUrl: string, postTitle: string, postExcerpt?: string, shareImage?: string) => {
  const [isWeChatReady, setIsWeChatReady] = useState(false);
  const { settings } = useShare();
  const { data: configData } = useQuery<WeChatConfigData>(GET_WECHAT_CONFIG, {
    variables: { url: postUrl },
    skip: typeof window === 'undefined' || !isWeChatBrowser(),
  });

  const hasConfig = !!configData?.wechatSdkConfig;

  // 判断是否在微信浏览器中
  function isWeChatBrowser() {
    return typeof navigator !== 'undefined' && /MicroMessenger/i.test(navigator.userAgent);
  }

  useEffect(() => {
    // 如果在微信环境，且配置数据已获取
    if (isWeChatBrowser() && configData?.wechatSdkConfig) {
      // 动态加载微信JS-SDK
      const script = document.createElement('script');
      script.src = 'https://res.wx.qq.com/open/js/jweixin-1.6.0.js';
      script.async = true;
      script.onload = () => {
        // 配置微信JS-SDK
        const { appId, timestamp, nonceStr, signature } = configData.wechatSdkConfig;
        
        // @ts-ignore - 微信SDK全局变量
        window.wx.config({
          debug: false,
          appId,
          timestamp,
          nonceStr,
          signature,
          jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData']
        });

        // @ts-ignore
        window.wx.ready(() => {
          setIsWeChatReady(true);
          
          // 生产环境下不再打印分享参数
          
          // 分享给朋友
          // @ts-ignore
          window.wx.updateAppMessageShareData({
            title: postTitle,
            desc: settings?.wechatDesc || postExcerpt || '',
            link: postUrl,
            imgUrl: shareImage || settings?.defaultThumb || '',
            success: () => {
              console.log('设置分享给朋友成功');
            }
          });
          
          // 分享到朋友圈
          // @ts-ignore
          window.wx.updateTimelineShareData({
            title: postTitle.split(' - ')[0] || postTitle, // 确保截取网站名称前的标题
            link: postUrl,
            imgUrl: shareImage || settings?.defaultThumb || '',
            success: () => {
              console.log('设置分享到朋友圈成功');
            }
          });
        });
      };
      
      document.body.appendChild(script);
      
      return () => {
        document.body.removeChild(script);
      };
    }
  }, [configData, postTitle, postUrl, postExcerpt, shareImage, settings]);
  
  return { 
    isWeChatBrowser: isWeChatBrowser(),
    isWeChatReady,
    hasConfig
  };
};

const platformIcons: { [key: string]: React.ReactNode } = {
  weibo: <FaWeibo size={20} />,
  wechat: <FaWeixin size={20} />,
  qq: <FaQq size={20} />,
  twitter: <FaXTwitter size={20} />, // 使用新的X图标
  // 可以继续添加其他平台
};

const platformNames: { [key: string]: string } = {
  weibo: '微博',
  wechat: '微信',
  qq: 'QQ',
  twitter: 'X',
};

const ShareButtons: React.FC<ShareButtonsProps> = ({ postId, postTitle, postUrl, postExcerpt, shareImage }) => {
  const { settings, isLoading } = useShare();
  const [isPosterModalOpen, setIsPosterModalOpen] = useState(false);
  const [isWeChatModalOpen, setIsWeChatModalOpen] = useState(false);
  const { isWeChatBrowser, isWeChatReady } = useWeChatShare(postUrl, postTitle, postExcerpt, shareImage);

  if (isLoading || !settings?.isEnabled) {
    return null; // 加载中或未启用分享功能时不显示任何内容
  }

  // 简单的设备检测
  const isMobile = typeof window !== 'undefined' && /Mobi|Android/i.test(navigator.userAgent);

  const getShareUrl = (platform: string): string => {
    const encodedUrl = encodeURIComponent(postUrl);
    const encodedTitle = encodeURIComponent(postTitle);
    const encodedImage = shareImage ? encodeURIComponent(shareImage) : 
                          settings?.defaultThumb ? encodeURIComponent(settings.defaultThumb) : '';

    switch (platform) {
      case 'weibo':
        return `http://service.weibo.com/share/share.php?url=${encodedUrl}&title=${encodedTitle}&pic=${encodedImage}`;
      case 'qq':
        return `http://connect.qq.com/widget/shareqq/index.html?url=${encodedUrl}&title=${encodedTitle}&summary=&pics=${encodedImage}`;
      case 'qzone':
        return `http://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?url=${encodedUrl}&title=${encodedTitle}&summary=&pics=${encodedImage}`;
      case 'twitter':
        return `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`;
      case 'facebook':
        return `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`;
      default:
        return '#';
    }
  };
  
  const handleWeChatShare = (e: React.MouseEvent) => {
    e.preventDefault();
    
    // 如果是微信浏览器，JS-SDK已经配置了分享
    if (isWeChatBrowser) {
      alert('请点击右上角的"..."按钮，然后选择"分享给朋友"或"分享到朋友圈"');
      return;
    }
    
    // 非微信浏览器但在移动设备上
    if (isMobile) {
      alert('请在微信内打开页面，通过右上角菜单进行分享。');
    } else {
      // 桌面端显示二维码
      setIsWeChatModalOpen(true);
    }
  };

  return (
    <>
      <div className="flex items-center space-x-3 my-6">
        <span className="text-sm font-semibold text-gray-600">分享到:</span>
        {settings.platforms?.map((platform) => {
          if (platform === 'wechat') {
            return (
              <button
                key={platform}
                onClick={handleWeChatShare}
                className="flex items-center justify-center w-9 h-9 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                aria-label={`分享到${platformNames[platform]}`}
              >
                {platformIcons[platform]}
              </button>
            );
          }
          return (
            <a
              key={platform}
              href={getShareUrl(platform)}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center w-9 h-9 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
              aria-label={`分享到${platformNames[platform]}`}
            >
              {platformIcons[platform]}
            </a>
          );
        })}
        {/* 海报生成按钮 */}
        <button
          onClick={() => setIsPosterModalOpen(true)}
          className="flex items-center justify-center w-9 h-9 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
          aria-label="生成分享海报"
        >
          <ImageIcon size={20} />
        </button>
      </div>

      {/* 海报生成模态框 */}
      <ShareModal
        isOpen={isPosterModalOpen}
        onClose={() => setIsPosterModalOpen(false)}
        postId={postId}
        postUrl={postUrl}
      />

      {/* 微信分享二维码模态框 (仅PC) */}
      {isWeChatModalOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={() => setIsWeChatModalOpen(false)}
        >
          <div 
            className="bg-white p-6 rounded-lg shadow-xl text-center"
            onClick={(e) => e.stopPropagation()}
          >
            <h3 className="text-lg font-semibold text-gray-800">微信扫码分享</h3>
            <div className="my-4 p-2 border border-gray-200 inline-block">
              <QRCodeSVG value={postUrl} size={160} />
            </div>
            <p className="text-sm text-gray-500">打开微信，扫一扫右上角的二维码</p>
          </div>
        </div>
      )}

      {/* 调试面板已移除 */}
    </>
  );
};

export default ShareButtons; 