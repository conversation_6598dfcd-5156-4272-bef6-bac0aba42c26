/**
 * 图片处理相关工具函数
 */

export interface ExtractedImage {
  src: string;
  alt?: string;
  caption?: string;
  width?: number;
  height?: number;
}

/**
 * 从HTML内容中提取所有图片
 * @param htmlContent HTML内容字符串
 * @returns 提取的图片信息数组
 */
export function extractImagesFromHTML(htmlContent: string): ExtractedImage[] {
  if (!htmlContent) return [];

  // 创建临时DOM元素来解析HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;

  const images: ExtractedImage[] = [];
  const imgElements = tempDiv.querySelectorAll('img');

  imgElements.forEach((img) => {
    const src = img.src || img.getAttribute('data-src');
    if (src) {
      images.push({
        src,
        alt: img.alt || '',
        caption: img.getAttribute('title') || '',
        width: img.width || undefined,
        height: img.height || undefined,
      });
    }
  });

  return images;
}

/**
 * 从WordPress Gutenberg块中提取图片
 * @param blocks Gutenberg块数组
 * @returns 提取的图片信息数组
 */
export function extractImagesFromBlocks(blocks: any[]): ExtractedImage[] {
  if (!blocks || !Array.isArray(blocks)) return [];

  const images: ExtractedImage[] = [];

  const processBlock = (block: any) => {
    if (!block) return;

    // 处理图片块
    if (block.__typename === 'CoreImageBlock' && block.attributes) {
      const { url, alt, caption, width, height } = block.attributes;
      if (url) {
        images.push({
          src: url,
          alt: alt || '',
          caption: caption || '',
          width: width || undefined,
          height: height || undefined,
        });
      }
    }

    // 处理媒体文本块中的图片
    if (block.__typename === 'CoreMediaTextBlock' && block.attributes?.mediaUrl) {
      const { mediaUrl, mediaAlt, mediaType } = block.attributes;
      if (mediaType === 'image' && mediaUrl) {
        images.push({
          src: mediaUrl,
          alt: mediaAlt || '',
        });
      }
    }

    // 处理画廊块
    if (block.__typename === 'CoreGalleryBlock' && block.attributes?.images) {
      block.attributes.images.forEach((image: any) => {
        if (image.url) {
          images.push({
            src: image.url,
            alt: image.alt || '',
            caption: image.caption || '',
          });
        }
      });
    }

    // 递归处理嵌套块（如列块、组块等）
    if (block.innerBlocks && Array.isArray(block.innerBlocks)) {
      block.innerBlocks.forEach(processBlock);
    }
  };

  blocks.forEach(processBlock);
  return images;
}

/**
 * 组合特色图片和内容图片
 * @param featuredImage 特色图片信息
 * @param contentImages 内容中的图片数组
 * @returns 合并后的图片数组
 */
export function combineImages(
  featuredImage?: { sourceUrl: string; altText?: string } | null,
  contentImages: ExtractedImage[] = []
): ExtractedImage[] {
  const allImages: ExtractedImage[] = [];

  // 添加特色图片作为第一张
  if (featuredImage?.sourceUrl) {
    allImages.push({
      src: featuredImage.sourceUrl,
      alt: featuredImage.altText || '',
    });
  }

  // 添加内容中的图片，但排除与特色图片重复的
  contentImages.forEach((img) => {
    // 检查是否与特色图片重复
    const isDuplicate = featuredImage?.sourceUrl === img.src;
    if (!isDuplicate) {
      allImages.push(img);
    }
  });

  return allImages;
}

/**
 * 检查图片URL是否有效
 * @param url 图片URL
 * @returns Promise<boolean>
 */
export function isValidImageUrl(url: string): Promise<boolean> {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;
  });
}

/**
 * 预加载图片
 * @param urls 图片URL数组
 * @returns Promise<void>
 */
export function preloadImages(urls: string[]): Promise<void[]> {
  const promises = urls.map((url) => {
    return new Promise<void>((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve();
      img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
      img.src = url;
    });
  });

  return Promise.all(promises);
}

/**
 * 获取图片的自然尺寸
 * @param url 图片URL
 * @returns Promise<{width: number, height: number}>
 */
export function getImageDimensions(url: string): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
    };
    img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
    img.src = url;
  });
}

/**
 * 从HTML内容中移除图片标签
 * @param htmlContent HTML内容字符串
 * @param imagesToRemove 要移除的图片URL数组
 * @returns 移除图片后的HTML内容
 */
export function removeImagesFromHTML(htmlContent: string, imagesToRemove: string[] = []): string {
  if (!htmlContent || imagesToRemove.length === 0) return htmlContent;

  // 创建临时DOM元素来处理HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;

  // 查找所有图片元素
  const imgElements = tempDiv.querySelectorAll('img');

  imgElements.forEach((img) => {
    const src = img.src || img.getAttribute('data-src');
    if (src && imagesToRemove.includes(src)) {
      // 查找父级figure元素
      const parentFigure = img.closest('figure');
      if (parentFigure) {
        // 如果图片在figure中，移除整个figure（包括caption）
        parentFigure.remove();
      } else {
        // 否则只移除图片元素
        img.remove();
      }
    }
  });

  return tempDiv.innerHTML;
}

/**
 * 从HTML内容中移除所有图片
 * @param htmlContent HTML内容字符串
 * @returns 移除所有图片后的HTML内容
 */
export function removeAllImagesFromHTML(htmlContent: string): string {
  if (!htmlContent) return htmlContent;

  let processedContent = htmlContent;

  // 1. 移除WordPress图片块（包含figure和figcaption）
  processedContent = processedContent.replace(
    /<figure[^>]*class="[^"]*wp-block-image[^"]*"[^>]*>.*?<\/figure>/gis,
    ''
  );

  // 2. 移除其他figure标签中的图片和caption
  processedContent = processedContent.replace(
    /<figure[^>]*>[\s\S]*?<img[^>]*>[\s\S]*?<\/figure>/gi,
    ''
  );

  // 3. 移除独立的img标签
  processedContent = processedContent.replace(/<img[^>]*>/gi, '');

  // 4. 移除可能残留的figcaption标签
  processedContent = processedContent.replace(/<figcaption[^>]*>.*?<\/figcaption>/gis, '');

  // 5. 清理多余的空白行
  processedContent = processedContent.replace(/\n\s*\n\s*\n/g, '\n\n');

  return processedContent;
}
